// import React from 'react'; // No longer needed with modern JSX transform
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import LoginPage from './components/auth/LoginPage';
import AdminDashboard from './components/admin/AdminDashboard';
import UserDashboard from './components/user/UserDashboard';
import { AuthProvider } from './context/AuthContext';
import { SensorDataProvider } from './context/SensorDataContext';
// import SensorList from './components/user/SensorList'; // Unused import
// import SensorDetail from './components/user/SensorDetail'; // Unused import
import ErrorBoundary from './components/ErrorBoundary';

// Simple test component for debugging
const TestComponent = () => {
  console.log('✅ TestComponent rendered');
  return (
    <div style={{ padding: '20px', backgroundColor: 'lightblue', minHeight: '100vh' }}>
      <h1>🧪 Test Component - Production Debug</h1>
      <p>If you can see this, React is working!</p>
      <p>Environment: {import.meta.env.MODE}</p>
      <p>Base URL: {import.meta.env.BASE_URL}</p>
      <p>Time: {new Date().toISOString()}</p>
      <button
        onClick={() => window.location.href = '/login'}
        style={{ padding: '10px', marginTop: '10px', backgroundColor: 'blue', color: 'white', border: 'none', borderRadius: '5px' }}
      >
        Go to Login (Test Routing)
      </button>
    </div>
  );
};

/**
 * Main application component that handles routing and context providers
 *
 * Wraps the entire application with:
 * - ErrorBoundary: Catches and handles React errors gracefully
 * - AuthProvider: Manages authentication state
 * - SensorDataProvider: Manages sensor data state
 * - Router: Handles client-side routing
 *
 * Routes:
 * - /login: Login page for authentication
 * - /admin/*: Admin dashboard and related routes
 * - /user/*: User dashboard and related routes
 * - /user/sensors: List of all sensors
 * - /user/sensors/:id: Detailed view of a specific sensor
 * - /: Redirects to login page
 *
 * @returns The main application component with routing and context providers
 */
function App() {
  console.log('🚀 App component rendering...');
  console.log('Environment mode:', import.meta.env.MODE);
  console.log('Base URL:', import.meta.env.BASE_URL);

  // Show test component first to verify React is working
  const showTestComponent = new URLSearchParams(window.location.search).get('test') === 'true';

  if (showTestComponent) {
    console.log('🧪 Test mode enabled via ?test=true');
    return <TestComponent />;
  }

  try {
    console.log('🔄 Rendering main application...');
    return (
      <ErrorBoundary>
        <AuthProvider>
          <SensorDataProvider>
            <Router>
              <Routes>
                <Route path="/login" element={<LoginPage />} />
                <Route path="/admin/*" element={<AdminDashboard />} />
                <Route path="/user/*" element={<UserDashboard />} />
                {/* Routes /user/sensors and /user/sensors/:id are handled within UserDashboard */}
                <Route path="/" element={<Navigate to="/login" replace />} />
              </Routes>
            </Router>
          </SensorDataProvider>
        </AuthProvider>
      </ErrorBoundary>
    );
  } catch (error) {
    console.error('❌ Error rendering main application:', error);
    return <TestComponent />;
  }
}

export default App;
