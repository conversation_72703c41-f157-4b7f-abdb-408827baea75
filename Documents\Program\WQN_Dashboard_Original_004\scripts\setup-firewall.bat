@echo off
REM Windows Firewall Configuration for WQN Dashboard
REM Run as Administrator

echo.
echo ========================================
echo  WQN Dashboard Firewall Setup
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Running as Administrator
) else (
    echo ❌ This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo.
echo 🔥 Configuring Windows Firewall for WQN Dashboard...
echo.

REM Remove existing rules (if any)
echo Removing existing rules...
netsh advfirewall firewall delete rule name="WQN Dashboard Server" >nul 2>&1
netsh advfirewall firewall delete rule name="WQN Dashboard Frontend" >nul 2>&1

REM Add inbound rule for server (port 3001)
echo Adding rule for server port 3001...
netsh advfirewall firewall add rule name="WQN Dashboard Server" dir=in action=allow protocol=TCP localport=3001

REM Add inbound rule for frontend (port 5173)
echo Adding rule for frontend port 5173...
netsh advfirewall firewall add rule name="WQN Dashboard Frontend" dir=in action=allow protocol=TCP localport=5173

REM Add outbound rules (optional, for external API access)
echo Adding outbound rule for external APIs...
netsh advfirewall firewall add rule name="WQN Dashboard API Access" dir=out action=allow protocol=TCP remoteport=443

echo.
echo ✅ Firewall rules configured successfully!
echo.
echo 📋 Rules added:
echo   - Inbound TCP port 3001 (Server)
echo   - Inbound TCP port 5173 (Frontend)
echo   - Outbound TCP port 443 (API Access)
echo.
echo 💡 Your WQN Dashboard should now be accessible from other devices on your network.
echo.

REM Display current rules
echo 🔍 Current firewall rules for WQN Dashboard:
netsh advfirewall firewall show rule name="WQN Dashboard Server"
netsh advfirewall firewall show rule name="WQN Dashboard Frontend"

echo.
echo ✅ Setup complete! You can now access your dashboard from other devices.
echo.
pause
