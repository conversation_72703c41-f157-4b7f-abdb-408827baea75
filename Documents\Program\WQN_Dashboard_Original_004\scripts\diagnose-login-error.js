#!/usr/bin/env node

/**
 * Login Error Diagnostic Script
 * Diagnoses and fixes common login 500 errors
 */

import { Sequelize, DataTypes } from 'sequelize';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import fetch from 'node-fetch';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const COLORS = {
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  CYAN: '\x1b[36m',
  RESET: '\x1b[0m',
  BOLD: '\x1b[1m'
};

function log(message, color = COLORS.RESET) {
  console.log(`${color}${message}${COLORS.RESET}`);
}

// Database configuration
const DB_PATH = path.join(__dirname, '..', 'src', 'server', 'database', 'water_quality.sqlite');

const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: DB_PATH,
  logging: false,
  define: {
    freezeTableName: true,
    timestamps: true
  }
});

// User model
const User = sequelize.define('User', {
  username: { type: DataTypes.STRING, primaryKey: true },
  fullName: DataTypes.STRING,
  email: DataTypes.STRING,
  role: DataTypes.STRING,
  status: DataTypes.STRING,
  password: DataTypes.STRING,
  lastActive: DataTypes.STRING
}, { freezeTableName: true, timestamps: true });

/**
 * Test database connection
 */
async function testDatabase() {
  try {
    log(`${COLORS.BLUE}📋 Testing database connection...${COLORS.RESET}`);
    await sequelize.authenticate();
    log(`${COLORS.GREEN}✅ Database connection successful${COLORS.RESET}`);
    return true;
  } catch (error) {
    log(`${COLORS.RED}❌ Database connection failed: ${error.message}${COLORS.RESET}`);
    return false;
  }
}

/**
 * Check users in database
 */
async function checkUsers() {
  try {
    log(`${COLORS.BLUE}📋 Checking users in database...${COLORS.RESET}`);
    const users = await User.findAll({
      attributes: ['username', 'email', 'role', 'status'],
      raw: true
    });
    
    if (users.length === 0) {
      log(`${COLORS.RED}❌ No users found in database${COLORS.RESET}`);
      return false;
    }
    
    log(`${COLORS.GREEN}✅ Found ${users.length} users:${COLORS.RESET}`);
    users.forEach(user => {
      log(`   - ${user.username} (${user.email}) - ${user.role} - ${user.status}`, COLORS.CYAN);
    });
    return true;
  } catch (error) {
    log(`${COLORS.RED}❌ Error checking users: ${error.message}${COLORS.RESET}`);
    return false;
  }
}

/**
 * Test JWT configuration
 */
async function testJWT() {
  try {
    log(`${COLORS.BLUE}📋 Testing JWT configuration...${COLORS.RESET}`);
    
    // Test JWT secret
    const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';
    
    if (JWT_SECRET.length < 32) {
      log(`${COLORS.YELLOW}⚠️  JWT secret is weak (${JWT_SECRET.length} chars)${COLORS.RESET}`);
    } else {
      log(`${COLORS.GREEN}✅ JWT secret is strong (${JWT_SECRET.length} chars)${COLORS.RESET}`);
    }
    
    // Test JWT creation and verification
    const testPayload = { username: 'test', role: 'user' };
    const token = jwt.sign(testPayload, JWT_SECRET, { expiresIn: '1h' });
    const decoded = jwt.verify(token, JWT_SECRET);
    
    log(`${COLORS.GREEN}✅ JWT creation and verification working${COLORS.RESET}`);
    return true;
  } catch (error) {
    log(`${COLORS.RED}❌ JWT test failed: ${error.message}${COLORS.RESET}`);
    return false;
  }
}

/**
 * Test login endpoint
 */
async function testLoginEndpoint(baseUrl = 'http://127.0.0.1:3001') {
  try {
    log(`${COLORS.BLUE}📋 Testing login endpoint at ${baseUrl}...${COLORS.RESET}`);
    
    const response = await fetch(`${baseUrl}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        identifier: 'admin',
        password: 'admin@123'
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      log(`${COLORS.GREEN}✅ Login endpoint working - Token received${COLORS.RESET}`);
      return true;
    } else {
      const errorText = await response.text();
      log(`${COLORS.RED}❌ Login failed - HTTP ${response.status}: ${errorText}${COLORS.RESET}`);
      return false;
    }
  } catch (error) {
    log(`${COLORS.RED}❌ Login endpoint test failed: ${error.message}${COLORS.RESET}`);
    return false;
  }
}

/**
 * Test CORS configuration
 */
async function testCORS(baseUrl = 'http://127.0.0.1:3001') {
  try {
    log(`${COLORS.BLUE}📋 Testing CORS configuration...${COLORS.RESET}`);
    
    const response = await fetch(`${baseUrl}/health`, {
      method: 'GET',
      headers: {
        'Origin': 'http://66.103.211.193:3001'
      }
    });
    
    const corsHeader = response.headers.get('access-control-allow-origin');
    if (corsHeader) {
      log(`${COLORS.GREEN}✅ CORS configured - Origin: ${corsHeader}${COLORS.RESET}`);
      return true;
    } else {
      log(`${COLORS.YELLOW}⚠️  No CORS headers found${COLORS.RESET}`);
      return false;
    }
  } catch (error) {
    log(`${COLORS.RED}❌ CORS test failed: ${error.message}${COLORS.RESET}`);
    return false;
  }
}

/**
 * Test password hashing
 */
async function testPasswordHashing() {
  try {
    log(`${COLORS.BLUE}📋 Testing password hashing...${COLORS.RESET}`);
    
    const testPassword = 'admin@123';
    const hashedPassword = await bcrypt.hash(testPassword, 10);
    const isValid = await bcrypt.compare(testPassword, hashedPassword);
    
    if (isValid) {
      log(`${COLORS.GREEN}✅ Password hashing working correctly${COLORS.RESET}`);
      return true;
    } else {
      log(`${COLORS.RED}❌ Password hashing failed${COLORS.RESET}`);
      return false;
    }
  } catch (error) {
    log(`${COLORS.RED}❌ Password hashing test failed: ${error.message}${COLORS.RESET}`);
    return false;
  }
}

/**
 * Fix common issues
 */
async function fixCommonIssues() {
  log(`${COLORS.YELLOW}🔧 Attempting to fix common issues...${COLORS.RESET}`);
  
  try {
    // Ensure admin user exists with correct password
    const adminUser = await User.findOne({ where: { username: 'admin' } });
    
    if (!adminUser) {
      log(`${COLORS.YELLOW}⚠️  Admin user not found, creating...${COLORS.RESET}`);
      const hashedPassword = await bcrypt.hash('admin@123', 12);
      await User.create({
        username: 'admin',
        fullName: 'System Admin',
        email: '<EMAIL>',
        role: 'admin',
        status: 'active',
        password: hashedPassword,
        lastActive: new Date().toISOString()
      });
      log(`${COLORS.GREEN}✅ Admin user created${COLORS.RESET}`);
    } else {
      // Verify admin password
      const isValidPassword = await bcrypt.compare('admin@123', adminUser.password);
      if (!isValidPassword) {
        log(`${COLORS.YELLOW}⚠️  Admin password incorrect, updating...${COLORS.RESET}`);
        const hashedPassword = await bcrypt.hash('admin@123', 12);
        await User.update(
          { password: hashedPassword },
          { where: { username: 'admin' } }
        );
        log(`${COLORS.GREEN}✅ Admin password updated${COLORS.RESET}`);
      }
    }
    
    return true;
  } catch (error) {
    log(`${COLORS.RED}❌ Fix failed: ${error.message}${COLORS.RESET}`);
    return false;
  }
}

/**
 * Main diagnostic function
 */
async function runDiagnostics() {
  log(`${COLORS.BOLD}${COLORS.CYAN}🔍 Login Error Diagnostic Tool${COLORS.RESET}`);
  log(`${COLORS.BOLD}${COLORS.CYAN}==============================${COLORS.RESET}\n`);
  
  const results = {
    database: false,
    users: false,
    jwt: false,
    passwordHashing: false,
    cors: false,
    loginLocal: false,
    loginExternal: false
  };
  
  // Test database
  results.database = await testDatabase();
  
  if (results.database) {
    results.users = await checkUsers();
    
    if (!results.users) {
      await fixCommonIssues();
      results.users = await checkUsers();
    }
  }
  
  // Test JWT
  results.jwt = await testJWT();
  
  // Test password hashing
  results.passwordHashing = await testPasswordHashing();
  
  // Test CORS
  results.cors = await testCORS();
  
  // Test login endpoints
  results.loginLocal = await testLoginEndpoint('http://127.0.0.1:3001');
  results.loginExternal = await testLoginEndpoint('http://66.103.211.193:3001');
  
  // Summary
  log(`\n${COLORS.BOLD}${COLORS.CYAN}📊 Diagnostic Summary:${COLORS.RESET}`);
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? `${COLORS.GREEN}✅ PASS` : `${COLORS.RED}❌ FAIL`;
    log(`   ${test}: ${status}${COLORS.RESET}`);
  });
  
  // Recommendations
  log(`\n${COLORS.BOLD}${COLORS.CYAN}💡 Recommendations:${COLORS.RESET}`);
  
  if (!results.database) {
    log(`   - Check database file exists and is accessible`, COLORS.YELLOW);
  }
  
  if (!results.users) {
    log(`   - Initialize database with users`, COLORS.YELLOW);
  }
  
  if (!results.jwt) {
    log(`   - Check JWT_SECRET environment variable`, COLORS.YELLOW);
  }
  
  if (!results.loginExternal) {
    log(`   - Check external IP access and firewall settings`, COLORS.YELLOW);
    log(`   - Verify CORS configuration includes external IP`, COLORS.YELLOW);
  }
  
  if (results.loginLocal && !results.loginExternal) {
    log(`   - Issue is likely network/firewall related, not application code`, COLORS.GREEN);
  }
  
  await sequelize.close();
}

// Run diagnostics
runDiagnostics().catch(error => {
  console.error('Diagnostic failed:', error.message);
  process.exit(1);
});
