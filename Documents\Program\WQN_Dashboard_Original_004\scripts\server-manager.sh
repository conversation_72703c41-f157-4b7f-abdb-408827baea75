#!/bin/bash

# WQN Dashboard Server Manager (No PM2)
# Simple script to start, stop, restart, and check status of the server

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    if [ "$2" = "OK" ]; then
        echo -e "${GREEN}✅ $1${NC}"
    elif [ "$2" = "WARN" ]; then
        echo -e "${YELLOW}⚠️  $1${NC}"
    elif [ "$2" = "ERROR" ]; then
        echo -e "${RED}❌ $1${NC}"
    else
        echo -e "${BLUE}ℹ️  $1${NC}"
    fi
}

# Function to check if server is running
check_server() {
    if [ -f "server.pid" ]; then
        PID=$(cat server.pid)
        if ps -p $PID > /dev/null 2>&1; then
            return 0  # Server is running
        else
            return 1  # PID file exists but process is not running
        fi
    else
        return 1  # No PID file
    fi
}

# Function to start the server
start_server() {
    echo "🚀 Starting WQN Dashboard Server..."
    
    # Check if already running
    if check_server; then
        PID=$(cat server.pid)
        print_status "Server is already running (PID: $PID)" "WARN"
        return 0
    fi
    
    # Remove stale PID file
    rm -f server.pid
    
    # Start the server
    print_status "Starting production server..." "INFO"
    nohup npm run start:prod > server.log 2>&1 &
    SERVER_PID=$!
    
    # Save PID
    echo $SERVER_PID > server.pid
    
    # Wait for server to start
    sleep 3
    
    # Check if server started successfully
    if check_server; then
        print_status "Server started successfully (PID: $SERVER_PID)" "OK"
        
        # Test health endpoint
        sleep 2
        if curl -s http://localhost:3001/health > /dev/null 2>&1; then
            print_status "Health endpoint responding" "OK"
        else
            print_status "Health endpoint not responding yet (may take a moment)" "WARN"
        fi
    else
        print_status "Failed to start server" "ERROR"
        print_status "Check server.log for errors" "INFO"
        return 1
    fi
}

# Function to stop the server
stop_server() {
    echo "🛑 Stopping WQN Dashboard Server..."
    
    if check_server; then
        PID=$(cat server.pid)
        print_status "Stopping server (PID: $PID)..." "INFO"
        
        # Try graceful shutdown first
        kill $PID 2>/dev/null
        
        # Wait for graceful shutdown
        sleep 3
        
        # Check if still running
        if ps -p $PID > /dev/null 2>&1; then
            print_status "Graceful shutdown failed, forcing kill..." "WARN"
            kill -9 $PID 2>/dev/null
            sleep 1
        fi
        
        # Verify stopped
        if ps -p $PID > /dev/null 2>&1; then
            print_status "Failed to stop server" "ERROR"
            return 1
        else
            print_status "Server stopped successfully" "OK"
            rm -f server.pid
        fi
    else
        print_status "Server is not running" "WARN"
    fi
    
    # Kill any remaining processes on port 3001
    pkill -f "node.*3001" 2>/dev/null || true
    pkill -f "tsx.*index.ts" 2>/dev/null || true
}

# Function to restart the server
restart_server() {
    echo "🔄 Restarting WQN Dashboard Server..."
    stop_server
    sleep 2
    start_server
}

# Function to show server status
show_status() {
    echo "📊 WQN Dashboard Server Status"
    echo "=============================="
    
    if check_server; then
        PID=$(cat server.pid)
        print_status "Server is running (PID: $PID)" "OK"
        
        # Check port
        if netstat -tuln 2>/dev/null | grep -q ":3001 "; then
            print_status "Port 3001 is listening" "OK"
        else
            print_status "Port 3001 is not listening" "ERROR"
        fi
        
        # Check health endpoint
        if curl -s http://localhost:3001/health > /dev/null 2>&1; then
            print_status "Health endpoint responding" "OK"
        else
            print_status "Health endpoint not responding" "ERROR"
        fi
        
        # Show process info
        echo ""
        echo "Process Information:"
        ps -p $PID -o pid,ppid,cmd,etime,pcpu,pmem 2>/dev/null || print_status "Process not found" "ERROR"
        
    else
        print_status "Server is not running" "ERROR"
        
        # Check if port is still occupied
        if netstat -tuln 2>/dev/null | grep -q ":3001 "; then
            print_status "Port 3001 is occupied by another process" "WARN"
            echo "Processes using port 3001:"
            lsof -i :3001 2>/dev/null || echo "Unable to determine process"
        fi
    fi
    
    # Show recent logs
    if [ -f "server.log" ]; then
        echo ""
        echo "Recent Logs (last 10 lines):"
        echo "============================="
        tail -10 server.log
    fi
}

# Function to show logs
show_logs() {
    if [ -f "server.log" ]; then
        echo "📋 Server Logs (press Ctrl+C to exit)"
        echo "====================================="
        tail -f server.log
    else
        print_status "No log file found (server.log)" "ERROR"
    fi
}

# Function to show help
show_help() {
    echo "🔧 WQN Dashboard Server Manager"
    echo "==============================="
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  start     Start the server"
    echo "  stop      Stop the server"
    echo "  restart   Restart the server"
    echo "  status    Show server status"
    echo "  logs      Show server logs (follow mode)"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 status"
    echo "  $0 logs"
    echo ""
    echo "Files:"
    echo "  server.pid    - Contains the server process ID"
    echo "  server.log    - Contains server output and errors"
    echo ""
    echo "URLs:"
    echo "  http://**************:3001      - Main application"
    echo "  http://**************:3001/health - Health check"
}

# Main script logic
case "$1" in
    start)
        start_server
        ;;
    stop)
        stop_server
        ;;
    restart)
        restart_server
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    help|--help|-h)
        show_help
        ;;
    "")
        echo "No command specified. Use '$0 help' for usage information."
        show_status
        ;;
    *)
        echo "Unknown command: $1"
        echo "Use '$0 help' for usage information."
        exit 1
        ;;
esac
