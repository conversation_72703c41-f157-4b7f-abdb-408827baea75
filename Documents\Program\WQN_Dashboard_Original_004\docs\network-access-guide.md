# Network Access Configuration Guide

## 🌐 **Production Network Access - ✅ FIXED**

Your production server is now configured to be accessible from all network interfaces.

---

## 🔧 **Changes Applied**

### **1. Server Configuration Updated**
```typescript
// ✅ BEFORE (localhost only)
server = app.listen(PORT, '127.0.0.1', () => {
  console.log(`Server running on http://127.0.0.1:${PORT}`);
});

// ✅ AFTER (all network interfaces)
const HOST = process.env.HOST || (process.env.NODE_ENV === 'production' ? '0.0.0.0' : '127.0.0.1');
server = app.listen(PORT, HOST, () => {
  console.log(`Server running on http://${HOST}:${PORT}`);
});
```

### **2. Environment Configuration**
```bash
# Added to .env.production
HOST=0.0.0.0  # Binds to all network interfaces
PORT=3001     # Production port
```

### **3. Vite Configuration**
```typescript
// Updated vite.config.ts
server: {
  host: true,  // Allow external access
  port: 3000,
  // ... proxy configuration
}
```

---

## 🚀 **How to Access Your Application**

### **Local Access**
```
http://localhost:3001
http://127.0.0.1:3001
```

### **Network Access**
```
http://[YOUR_IP_ADDRESS]:3001
```

### **Find Your IP Address**

**Windows:**
```cmd
ipconfig
```
Look for "IPv4 Address" under your active network adapter.

**Linux/Mac:**
```bash
ip addr show
# or
ifconfig
```

**Example:**
If your IP is `*************`, access via:
```
http://*************:3001
```

---

## 🔒 **Security Considerations**

### **Firewall Configuration**
Ensure port 3001 is open in your firewall:

**Windows Firewall:**
```cmd
netsh advfirewall firewall add rule name="WQN Dashboard" dir=in action=allow protocol=TCP localport=3001
```

**Linux (ufw):**
```bash
sudo ufw allow 3001
```

### **Network Security**
- ✅ CORS is configured for production domains
- ✅ Rate limiting is enabled
- ✅ Security headers are applied
- ✅ Input sanitization is active

### **Production Recommendations**
1. **Use HTTPS** in production with SSL certificates
2. **Configure reverse proxy** (nginx/Apache) for better security
3. **Set specific CORS origins** instead of allowing all
4. **Use environment variables** for sensitive configuration

---

## 🛠️ **Troubleshooting**

### **Still Can't Access from Network?**

1. **Check Firewall:**
   ```cmd
   # Windows - Check if port is blocked
   netstat -an | findstr :3001
   ```

2. **Verify Server Binding:**
   ```bash
   # Should show 0.0.0.0:3001, not 127.0.0.1:3001
   netstat -tulpn | grep :3001
   ```

3. **Test Network Connectivity:**
   ```cmd
   # From another device on the network
   telnet [SERVER_IP] 3001
   ```

4. **Check Environment Variables:**
   ```bash
   echo $HOST
   echo $PORT
   ```

### **Common Issues**

**Issue:** "Connection refused"
**Solution:** Check if HOST=0.0.0.0 is set in production environment

**Issue:** "Timeout"
**Solution:** Check firewall settings and network configuration

**Issue:** "CORS errors"
**Solution:** Update CORS_ORIGIN in .env.production with your domain

---

## 📱 **Mobile/Remote Access**

### **Same Network Access**
Devices on the same WiFi/LAN can access via:
```
http://[SERVER_IP]:3001
```

### **Remote Access (Advanced)**
For access from outside your network:
1. **Port Forwarding:** Configure router to forward port 3001
2. **VPN:** Set up VPN for secure remote access
3. **Cloud Deployment:** Deploy to cloud provider (AWS, Azure, etc.)

---

## 🔄 **Restart Instructions**

After making these changes, restart your production server:

```bash
# Stop current server (Ctrl+C)
# Then restart
npm run start:prod
```

You should now see:
```
🌐 Production server accessible on all network interfaces
📱 Access from other devices: http://[YOUR_IP]:3001
```

---

## ✅ **Verification Checklist**

- [ ] Server starts with `0.0.0.0` binding
- [ ] Local access works: `http://localhost:3001`
- [ ] Network access works: `http://[YOUR_IP]:3001`
- [ ] Firewall allows port 3001
- [ ] Other devices can connect
- [ ] HTTPS configured (production)
- [ ] CORS properly configured

Your application is now accessible from all network interfaces! 🎉
