#!/bin/bash

# Remote Server Diagnostic Script for WQN Dashboard
# Run this script on the remote server (**************) to diagnose issues

echo "🔍 WQN Dashboard Remote Server Diagnostic"
echo "=========================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    if [ "$2" = "OK" ]; then
        echo -e "${GREEN}✅ $1${NC}"
    elif [ "$2" = "WARN" ]; then
        echo -e "${YELLOW}⚠️  $1${NC}"
    elif [ "$2" = "ERROR" ]; then
        echo -e "${RED}❌ $1${NC}"
    else
        echo -e "${BLUE}ℹ️  $1${NC}"
    fi
}

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    print_status "Running as root - good for system checks" "WARN"
else
    print_status "Running as non-root user" "INFO"
fi

echo ""
echo "🖥️  System Information:"
echo "======================"
print_status "OS: $(lsb_release -d 2>/dev/null | cut -f2 || echo 'Unknown')" "INFO"
print_status "Kernel: $(uname -r)" "INFO"
print_status "Architecture: $(uname -m)" "INFO"
print_status "Uptime: $(uptime -p 2>/dev/null || uptime)" "INFO"

echo ""
echo "🔧 Node.js & NPM:"
echo "=================="
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    print_status "Node.js version: $NODE_VERSION" "OK"
    
    if [[ "$NODE_VERSION" =~ v1[8-9]\.|v[2-9][0-9]\. ]]; then
        print_status "Node.js version is compatible" "OK"
    else
        print_status "Node.js version may be too old (need 18+)" "WARN"
    fi
else
    print_status "Node.js not found" "ERROR"
fi

if command -v npm &> /dev/null; then
    print_status "NPM version: $(npm --version)" "OK"
else
    print_status "NPM not found" "ERROR"
fi

echo ""
echo "📁 Application Files:"
echo "===================="
if [ -f "package.json" ]; then
    print_status "package.json found" "OK"
else
    print_status "package.json not found - wrong directory?" "ERROR"
fi

if [ -f ".env.production" ]; then
    print_status ".env.production found" "OK"
else
    print_status ".env.production not found" "WARN"
fi

if [ -d "node_modules" ]; then
    print_status "node_modules directory exists" "OK"
else
    print_status "node_modules not found - run npm install" "ERROR"
fi

if [ -d "dist" ]; then
    print_status "dist directory exists (built application)" "OK"
else
    print_status "dist directory not found - run npm run build" "WARN"
fi

echo ""
echo "🗄️  Database:"
echo "============="
if [ -f "src/server/database/water_quality.sqlite" ]; then
    print_status "SQLite database file exists" "OK"
    
    # Check database permissions
    if [ -r "src/server/database/water_quality.sqlite" ]; then
        print_status "Database file is readable" "OK"
    else
        print_status "Database file is not readable" "ERROR"
    fi
    
    if [ -w "src/server/database/water_quality.sqlite" ]; then
        print_status "Database file is writable" "OK"
    else
        print_status "Database file is not writable" "ERROR"
    fi
    
    # Check database size
    DB_SIZE=$(du -h "src/server/database/water_quality.sqlite" | cut -f1)
    print_status "Database size: $DB_SIZE" "INFO"
else
    print_status "Database file not found" "ERROR"
fi

echo ""
echo "🌐 Network & Ports:"
echo "=================="

# Check if port 3001 is listening
if netstat -tuln 2>/dev/null | grep -q ":3001 "; then
    print_status "Port 3001 is listening" "OK"
    
    # Check what's binding to it
    BINDING=$(netstat -tuln 2>/dev/null | grep ":3001 " | head -1)
    if echo "$BINDING" | grep -q "0.0.0.0:3001"; then
        print_status "Port 3001 bound to all interfaces (0.0.0.0)" "OK"
    elif echo "$BINDING" | grep -q "127.0.0.1:3001"; then
        print_status "Port 3001 bound to localhost only" "WARN"
    else
        print_status "Port 3001 binding: $BINDING" "INFO"
    fi
else
    print_status "Port 3001 is not listening" "ERROR"
fi

# Check firewall status
if command -v ufw &> /dev/null; then
    UFW_STATUS=$(ufw status 2>/dev/null | head -1)
    print_status "UFW Firewall: $UFW_STATUS" "INFO"
    
    if ufw status 2>/dev/null | grep -q "3001"; then
        print_status "Port 3001 allowed in UFW" "OK"
    else
        print_status "Port 3001 not explicitly allowed in UFW" "WARN"
    fi
elif command -v firewall-cmd &> /dev/null; then
    if firewall-cmd --state &> /dev/null; then
        print_status "Firewalld is active" "INFO"
        if firewall-cmd --list-ports 2>/dev/null | grep -q "3001"; then
            print_status "Port 3001 allowed in firewalld" "OK"
        else
            print_status "Port 3001 not allowed in firewalld" "WARN"
        fi
    else
        print_status "Firewalld is not running" "INFO"
    fi
else
    print_status "No firewall management tool found" "INFO"
fi

echo ""
echo "🔄 Process Management:"
echo "====================="

# Check if PM2 is installed
if command -v pm2 &> /dev/null; then
    print_status "PM2 is installed" "OK"
    
    # Check PM2 processes
    PM2_PROCESSES=$(pm2 list 2>/dev/null | grep -c "online\|stopped\|errored")
    if [ "$PM2_PROCESSES" -gt 0 ]; then
        print_status "PM2 processes found: $PM2_PROCESSES" "INFO"
        pm2 list 2>/dev/null | grep -E "App name|wqn|dashboard" || true
    else
        print_status "No PM2 processes running" "WARN"
    fi
else
    print_status "PM2 not installed" "WARN"
fi

# Check for Node.js processes
NODE_PROCESSES=$(ps aux | grep -c "[n]ode.*3001\|[n]ode.*wqn\|[n]ode.*dashboard")
if [ "$NODE_PROCESSES" -gt 0 ]; then
    print_status "Node.js processes found: $NODE_PROCESSES" "OK"
else
    print_status "No Node.js processes found" "WARN"
fi

echo ""
echo "🔐 Environment & Security:"
echo "========================="

# Check environment variables
if [ -f ".env.production" ]; then
    if grep -q "JWT_SECRET" .env.production; then
        JWT_LENGTH=$(grep "JWT_SECRET" .env.production | cut -d'=' -f2 | wc -c)
        if [ "$JWT_LENGTH" -gt 32 ]; then
            print_status "JWT_SECRET is configured and strong" "OK"
        else
            print_status "JWT_SECRET is too weak" "WARN"
        fi
    else
        print_status "JWT_SECRET not found in .env.production" "ERROR"
    fi
    
    if grep -q "NODE_ENV=production" .env.production; then
        print_status "NODE_ENV set to production" "OK"
    else
        print_status "NODE_ENV not set to production" "WARN"
    fi
else
    print_status "Cannot check environment variables - .env.production missing" "ERROR"
fi

echo ""
echo "🧪 Quick Tests:"
echo "==============="

# Test localhost connectivity
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3001/health 2>/dev/null | grep -q "200"; then
    print_status "Health endpoint responds on localhost" "OK"
else
    print_status "Health endpoint not responding on localhost" "ERROR"
fi

# Test external connectivity
EXTERNAL_IP=$(curl -s ifconfig.me 2>/dev/null || echo "unknown")
print_status "External IP: $EXTERNAL_IP" "INFO"

echo ""
echo "📋 Recommendations:"
echo "=================="

if [ ! -f ".env.production" ]; then
    echo "• Create .env.production file with proper configuration"
fi

if ! netstat -tuln 2>/dev/null | grep -q ":3001 "; then
    echo "• Start the WQN Dashboard application"
    echo "  - npm run start:prod"
    echo "  - or pm2 start ecosystem.config.js"
fi

if netstat -tuln 2>/dev/null | grep -q "127.0.0.1:3001"; then
    echo "• Configure server to bind to all interfaces (0.0.0.0)"
    echo "  - Set HOST=0.0.0.0 in .env.production"
fi

if ! command -v pm2 &> /dev/null; then
    echo "• Install PM2 for better process management:"
    echo "  - npm install -g pm2"
fi

if command -v ufw &> /dev/null && ! ufw status 2>/dev/null | grep -q "3001"; then
    echo "• Allow port 3001 in firewall:"
    echo "  - sudo ufw allow 3001/tcp"
fi

echo ""
echo "🔧 Quick Fix Commands:"
echo "====================="
echo "# Install dependencies:"
echo "npm ci --production"
echo ""
echo "# Build application:"
echo "npm run build"
echo ""
echo "# Initialize database:"
echo "npm run migrate"
echo ""
echo "# Reset admin password:"
echo "npm run reset-admin-password"
echo ""
echo "# Start with PM2:"
echo "pm2 start scripts/start-production.js --name wqn-dashboard"
echo ""
echo "# Allow firewall (Ubuntu):"
echo "sudo ufw allow 3001/tcp"
echo ""
echo "# Check logs:"
echo "pm2 logs wqn-dashboard"

echo ""
echo "✅ Diagnostic complete!"
echo "📞 If issues persist, check the logs and verify network connectivity."
