var t=Object.defineProperty,e=(e,n,r)=>((e,n,r)=>n in e?t(e,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[n]=r)(e,"symbol"!=typeof n?n+"":n,r);function n(t,e){return function(){return t.apply(e,arguments)}}const{toString:r}=Object.prototype,{getPrototypeOf:o}=Object,a=(t=>e=>{const n=r.call(e);return t[n]||(t[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),s=t=>(t=t.toLowerCase(),e=>a(e)===t),i=t=>e=>typeof e===t,{isArray:u}=Array,c=i("undefined");const l=s("ArrayBuffer");const d=i("string"),h=i("function"),f=i("number"),m=t=>null!==t&&"object"==typeof t,p=t=>{if("object"!==a(t))return!1;const e=o(t);return!(null!==e&&e!==Object.prototype&&null!==Object.getPrototypeOf(e)||Symbol.toStringTag in t||Symbol.iterator in t)},w=s("Date"),g=s("File"),b=s("Blob"),y=s("FileList"),x=s("URLSearchParams"),[T,v,S,E]=["ReadableStream","Request","Response","Headers"].map(s);function O(t,e,{allOwnKeys:n=!1}={}){if(null==t)return;let r,o;if("object"!=typeof t&&(t=[t]),u(t))for(r=0,o=t.length;r<o;r++)e.call(null,t[r],r,t);else{const o=n?Object.getOwnPropertyNames(t):Object.keys(t),a=o.length;let s;for(r=0;r<a;r++)s=o[r],e.call(null,t[s],s,t)}}function k(t,e){e=e.toLowerCase();const n=Object.keys(t);let r,o=n.length;for(;o-- >0;)if(r=n[o],e===r.toLowerCase())return r;return null}const M="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,D=t=>!c(t)&&t!==M;const N=(t=>e=>t&&e instanceof t)("undefined"!=typeof Uint8Array&&o(Uint8Array)),R=s("HTMLFormElement"),P=(({hasOwnProperty:t})=>(e,n)=>t.call(e,n))(Object.prototype),C=s("RegExp"),A=(t,e)=>{const n=Object.getOwnPropertyDescriptors(t),r={};O(n,((n,o)=>{let a;!1!==(a=e(n,o,t))&&(r[o]=a||n)})),Object.defineProperties(t,r)};const F=s("AsyncFunction"),j=(L="function"==typeof setImmediate,q=h(M.postMessage),L?setImmediate:q?(H=`axios@${Math.random()}`,B=[],M.addEventListener("message",(({source:t,data:e})=>{t===M&&e===H&&B.length&&B.shift()()}),!1),t=>{B.push(t),M.postMessage(H,"*")}):t=>setTimeout(t));var L,q,H,B;const U="undefined"!=typeof queueMicrotask?queueMicrotask.bind(M):"undefined"!=typeof process&&process.nextTick||j,Y={isArray:u,isArrayBuffer:l,isBuffer:function(t){return null!==t&&!c(t)&&null!==t.constructor&&!c(t.constructor)&&h(t.constructor.isBuffer)&&t.constructor.isBuffer(t)},isFormData:t=>{let e;return t&&("function"==typeof FormData&&t instanceof FormData||h(t.append)&&("formdata"===(e=a(t))||"object"===e&&h(t.toString)&&"[object FormData]"===t.toString()))},isArrayBufferView:function(t){let e;return e="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer&&l(t.buffer),e},isString:d,isNumber:f,isBoolean:t=>!0===t||!1===t,isObject:m,isPlainObject:p,isReadableStream:T,isRequest:v,isResponse:S,isHeaders:E,isUndefined:c,isDate:w,isFile:g,isBlob:b,isRegExp:C,isFunction:h,isStream:t=>m(t)&&h(t.pipe),isURLSearchParams:x,isTypedArray:N,isFileList:y,forEach:O,merge:function t(){const{caseless:e}=D(this)&&this||{},n={},r=(r,o)=>{const a=e&&k(n,o)||o;p(n[a])&&p(r)?n[a]=t(n[a],r):p(r)?n[a]=t({},r):u(r)?n[a]=r.slice():n[a]=r};for(let o=0,a=arguments.length;o<a;o++)arguments[o]&&O(arguments[o],r);return n},extend:(t,e,r,{allOwnKeys:o}={})=>(O(e,((e,o)=>{r&&h(e)?t[o]=n(e,r):t[o]=e}),{allOwnKeys:o}),t),trim:t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:t=>(65279===t.charCodeAt(0)&&(t=t.slice(1)),t),inherits:(t,e,n,r)=>{t.prototype=Object.create(e.prototype,r),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:e.prototype}),n&&Object.assign(t.prototype,n)},toFlatObject:(t,e,n,r)=>{let a,s,i;const u={};if(e=e||{},null==t)return e;do{for(a=Object.getOwnPropertyNames(t),s=a.length;s-- >0;)i=a[s],r&&!r(i,t,e)||u[i]||(e[i]=t[i],u[i]=!0);t=!1!==n&&o(t)}while(t&&(!n||n(t,e))&&t!==Object.prototype);return e},kindOf:a,kindOfTest:s,endsWith:(t,e,n)=>{t=String(t),(void 0===n||n>t.length)&&(n=t.length),n-=e.length;const r=t.indexOf(e,n);return-1!==r&&r===n},toArray:t=>{if(!t)return null;if(u(t))return t;let e=t.length;if(!f(e))return null;const n=new Array(e);for(;e-- >0;)n[e]=t[e];return n},forEachEntry:(t,e)=>{const n=(t&&t[Symbol.iterator]).call(t);let r;for(;(r=n.next())&&!r.done;){const n=r.value;e.call(t,n[0],n[1])}},matchAll:(t,e)=>{let n;const r=[];for(;null!==(n=t.exec(e));)r.push(n);return r},isHTMLForm:R,hasOwnProperty:P,hasOwnProp:P,reduceDescriptors:A,freezeMethods:t=>{A(t,((e,n)=>{if(h(t)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=t[n];h(r)&&(e.enumerable=!1,"writable"in e?e.writable=!1:e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(t,e)=>{const n={},r=t=>{t.forEach((t=>{n[t]=!0}))};return u(t)?r(t):r(String(t).split(e)),n},toCamelCase:t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(t,e,n){return e.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(t,e)=>null!=t&&Number.isFinite(t=+t)?t:e,findKey:k,global:M,isContextDefined:D,isSpecCompliantForm:function(t){return!!(t&&h(t.append)&&"FormData"===t[Symbol.toStringTag]&&t[Symbol.iterator])},toJSONObject:t=>{const e=new Array(10),n=(t,r)=>{if(m(t)){if(e.indexOf(t)>=0)return;if(!("toJSON"in t)){e[r]=t;const o=u(t)?[]:{};return O(t,((t,e)=>{const a=n(t,r+1);!c(a)&&(o[e]=a)})),e[r]=void 0,o}}return t};return n(t,0)},isAsyncFn:F,isThenable:t=>t&&(m(t)||h(t))&&h(t.then)&&h(t.catch),setImmediate:j,asap:U};function W(t,e,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=t,this.name="AxiosError",e&&(this.code=e),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}Y.inherits(W,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:Y.toJSONObject(this.config),code:this.code,status:this.status}}});const _=W.prototype,I={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((t=>{I[t]={value:t}})),Object.defineProperties(W,I),Object.defineProperty(_,"isAxiosError",{value:!0}),W.from=(t,e,n,r,o,a)=>{const s=Object.create(_);return Y.toFlatObject(t,s,(function(t){return t!==Error.prototype}),(t=>"isAxiosError"!==t)),W.call(s,t.message,e,n,r,o),s.cause=t,s.name=t.name,a&&Object.assign(s,a),s};function z(t){return Y.isPlainObject(t)||Y.isArray(t)}function Q(t){return Y.endsWith(t,"[]")?t.slice(0,-2):t}function X(t,e,n){return t?t.concat(e).map((function(t,e){return t=Q(t),!n&&e?"["+t+"]":t})).join(n?".":""):e}const G=Y.toFlatObject(Y,{},null,(function(t){return/^is[A-Z]/.test(t)}));function $(t,e,n){if(!Y.isObject(t))throw new TypeError("target must be an object");e=e||new FormData;const r=(n=Y.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(t,e){return!Y.isUndefined(e[t])}))).metaTokens,o=n.visitor||c,a=n.dots,s=n.indexes,i=(n.Blob||"undefined"!=typeof Blob&&Blob)&&Y.isSpecCompliantForm(e);if(!Y.isFunction(o))throw new TypeError("visitor must be a function");function u(t){if(null===t)return"";if(Y.isDate(t))return t.toISOString();if(!i&&Y.isBlob(t))throw new W("Blob is not supported. Use a Buffer instead.");return Y.isArrayBuffer(t)||Y.isTypedArray(t)?i&&"function"==typeof Blob?new Blob([t]):Buffer.from(t):t}function c(t,n,o){let i=t;if(t&&!o&&"object"==typeof t)if(Y.endsWith(n,"{}"))n=r?n:n.slice(0,-2),t=JSON.stringify(t);else if(Y.isArray(t)&&function(t){return Y.isArray(t)&&!t.some(z)}(t)||(Y.isFileList(t)||Y.endsWith(n,"[]"))&&(i=Y.toArray(t)))return n=Q(n),i.forEach((function(t,r){!Y.isUndefined(t)&&null!==t&&e.append(!0===s?X([n],r,a):null===s?n:n+"[]",u(t))})),!1;return!!z(t)||(e.append(X(o,n,a),u(t)),!1)}const l=[],d=Object.assign(G,{defaultVisitor:c,convertValue:u,isVisitable:z});if(!Y.isObject(t))throw new TypeError("data must be an object");return function t(n,r){if(!Y.isUndefined(n)){if(-1!==l.indexOf(n))throw Error("Circular reference detected in "+r.join("."));l.push(n),Y.forEach(n,(function(n,a){!0===(!(Y.isUndefined(n)||null===n)&&o.call(e,n,Y.isString(a)?a.trim():a,r,d))&&t(n,r?r.concat(a):[a])})),l.pop()}}(t),e}function J(t){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,(function(t){return e[t]}))}function K(t,e){this._pairs=[],t&&$(t,this,e)}const V=K.prototype;function Z(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function tt(t,e,n){if(!e)return t;const r=n&&n.encode||Z;Y.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let a;if(a=o?o(e,n):Y.isURLSearchParams(e)?e.toString():new K(e,n).toString(r),a){const e=t.indexOf("#");-1!==e&&(t=t.slice(0,e)),t+=(-1===t.indexOf("?")?"?":"&")+a}return t}V.append=function(t,e){this._pairs.push([t,e])},V.toString=function(t){const e=t?function(e){return t.call(this,e,J)}:J;return this._pairs.map((function(t){return e(t[0])+"="+e(t[1])}),"").join("&")};class et{constructor(){this.handlers=[]}use(t,e,n){return this.handlers.push({fulfilled:t,rejected:e,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){Y.forEach(this.handlers,(function(e){null!==e&&t(e)}))}}const nt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},rt={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:K,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},ot="undefined"!=typeof window&&"undefined"!=typeof document,at="object"==typeof navigator&&navigator||void 0,st=ot&&(!at||["ReactNative","NativeScript","NS"].indexOf(at.product)<0),it="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,ut=ot&&window.location.href||"http://localhost",ct={...Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ot,hasStandardBrowserEnv:st,hasStandardBrowserWebWorkerEnv:it,navigator:at,origin:ut},Symbol.toStringTag,{value:"Module"})),...rt};function lt(t){function e(t,n,r,o){let a=t[o++];if("__proto__"===a)return!0;const s=Number.isFinite(+a),i=o>=t.length;if(a=!a&&Y.isArray(r)?r.length:a,i)return Y.hasOwnProp(r,a)?r[a]=[r[a],n]:r[a]=n,!s;r[a]&&Y.isObject(r[a])||(r[a]=[]);return e(t,n,r[a],o)&&Y.isArray(r[a])&&(r[a]=function(t){const e={},n=Object.keys(t);let r;const o=n.length;let a;for(r=0;r<o;r++)a=n[r],e[a]=t[a];return e}(r[a])),!s}if(Y.isFormData(t)&&Y.isFunction(t.entries)){const n={};return Y.forEachEntry(t,((t,r)=>{e(function(t){return Y.matchAll(/\w+|\[(\w*)]/g,t).map((t=>"[]"===t[0]?"":t[1]||t[0]))}(t),r,n,0)})),n}return null}const dt={transitional:nt,adapter:["xhr","http","fetch"],transformRequest:[function(t,e){const n=e.getContentType()||"",r=n.indexOf("application/json")>-1,o=Y.isObject(t);o&&Y.isHTMLForm(t)&&(t=new FormData(t));if(Y.isFormData(t))return r?JSON.stringify(lt(t)):t;if(Y.isArrayBuffer(t)||Y.isBuffer(t)||Y.isStream(t)||Y.isFile(t)||Y.isBlob(t)||Y.isReadableStream(t))return t;if(Y.isArrayBufferView(t))return t.buffer;if(Y.isURLSearchParams(t))return e.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(t,e){return $(t,new ct.classes.URLSearchParams,Object.assign({visitor:function(t,e,n,r){return ct.isNode&&Y.isBuffer(t)?(this.append(e,t.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},e))}(t,this.formSerializer).toString();if((a=Y.isFileList(t))||n.indexOf("multipart/form-data")>-1){const e=this.env&&this.env.FormData;return $(a?{"files[]":t}:t,e&&new e,this.formSerializer)}}return o||r?(e.setContentType("application/json",!1),function(t,e){if(Y.isString(t))try{return(e||JSON.parse)(t),Y.trim(t)}catch(n){if("SyntaxError"!==n.name)throw n}return(0,JSON.stringify)(t)}(t)):t}],transformResponse:[function(t){const e=this.transitional||dt.transitional,n=e&&e.forcedJSONParsing,r="json"===this.responseType;if(Y.isResponse(t)||Y.isReadableStream(t))return t;if(t&&Y.isString(t)&&(n&&!this.responseType||r)){const n=!(e&&e.silentJSONParsing)&&r;try{return JSON.parse(t)}catch(o){if(n){if("SyntaxError"===o.name)throw W.from(o,W.ERR_BAD_RESPONSE,this,null,this.response);throw o}}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ct.classes.FormData,Blob:ct.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};Y.forEach(["delete","get","head","post","put","patch"],(t=>{dt.headers[t]={}}));const ht=Y.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),ft=Symbol("internals");function mt(t){return t&&String(t).trim().toLowerCase()}function pt(t){return!1===t||null==t?t:Y.isArray(t)?t.map(pt):String(t)}function wt(t,e,n,r,o){return Y.isFunction(r)?r.call(this,e,n):(o&&(e=n),Y.isString(e)?Y.isString(r)?-1!==e.indexOf(r):Y.isRegExp(r)?r.test(e):void 0:void 0)}class gt{constructor(t){t&&this.set(t)}set(t,e,n){const r=this;function o(t,e,n){const o=mt(e);if(!o)throw new Error("header name must be a non-empty string");const a=Y.findKey(r,o);(!a||void 0===r[a]||!0===n||void 0===n&&!1!==r[a])&&(r[a||e]=pt(t))}const a=(t,e)=>Y.forEach(t,((t,n)=>o(t,n,e)));if(Y.isPlainObject(t)||t instanceof this.constructor)a(t,e);else if(Y.isString(t)&&(t=t.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim()))a((t=>{const e={};let n,r,o;return t&&t.split("\n").forEach((function(t){o=t.indexOf(":"),n=t.substring(0,o).trim().toLowerCase(),r=t.substring(o+1).trim(),!n||e[n]&&ht[n]||("set-cookie"===n?e[n]?e[n].push(r):e[n]=[r]:e[n]=e[n]?e[n]+", "+r:r)})),e})(t),e);else if(Y.isHeaders(t))for(const[s,i]of t.entries())o(i,s,n);else null!=t&&o(e,t,n);return this}get(t,e){if(t=mt(t)){const n=Y.findKey(this,t);if(n){const t=this[n];if(!e)return t;if(!0===e)return function(t){const e=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(t);)e[r[1]]=r[2];return e}(t);if(Y.isFunction(e))return e.call(this,t,n);if(Y.isRegExp(e))return e.exec(t);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,e){if(t=mt(t)){const n=Y.findKey(this,t);return!(!n||void 0===this[n]||e&&!wt(0,this[n],n,e))}return!1}delete(t,e){const n=this;let r=!1;function o(t){if(t=mt(t)){const o=Y.findKey(n,t);!o||e&&!wt(0,n[o],o,e)||(delete n[o],r=!0)}}return Y.isArray(t)?t.forEach(o):o(t),r}clear(t){const e=Object.keys(this);let n=e.length,r=!1;for(;n--;){const o=e[n];t&&!wt(0,this[o],o,t,!0)||(delete this[o],r=!0)}return r}normalize(t){const e=this,n={};return Y.forEach(this,((r,o)=>{const a=Y.findKey(n,o);if(a)return e[a]=pt(r),void delete e[o];const s=t?function(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((t,e,n)=>e.toUpperCase()+n))}(o):String(o).trim();s!==o&&delete e[o],e[s]=pt(r),n[s]=!0})),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const e=Object.create(null);return Y.forEach(this,((n,r)=>{null!=n&&!1!==n&&(e[r]=t&&Y.isArray(n)?n.join(", "):n)})),e}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([t,e])=>t+": "+e)).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...e){const n=new this(t);return e.forEach((t=>n.set(t))),n}static accessor(t){const e=(this[ft]=this[ft]={accessors:{}}).accessors,n=this.prototype;function r(t){const r=mt(t);e[r]||(!function(t,e){const n=Y.toCamelCase(" "+e);["get","set","has"].forEach((r=>{Object.defineProperty(t,r+n,{value:function(t,n,o){return this[r].call(this,e,t,n,o)},configurable:!0})}))}(n,t),e[r]=!0)}return Y.isArray(t)?t.forEach(r):r(t),this}}function bt(t,e){const n=this||dt,r=e||n,o=gt.from(r.headers);let a=r.data;return Y.forEach(t,(function(t){a=t.call(n,a,o.normalize(),e?e.status:void 0)})),o.normalize(),a}function yt(t){return!(!t||!t.__CANCEL__)}function xt(t,e,n){W.call(this,null==t?"canceled":t,W.ERR_CANCELED,e,n),this.name="CanceledError"}function Tt(t,e,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?e(new W("Request failed with status code "+n.status,[W.ERR_BAD_REQUEST,W.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):t(n)}gt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),Y.reduceDescriptors(gt.prototype,(({value:t},e)=>{let n=e[0].toUpperCase()+e.slice(1);return{get:()=>t,set(t){this[n]=t}}})),Y.freezeMethods(gt),Y.inherits(xt,W,{__CANCEL__:!0});const vt=(t,e,n=3)=>{let r=0;const o=function(t,e){t=t||10;const n=new Array(t),r=new Array(t);let o,a=0,s=0;return e=void 0!==e?e:1e3,function(i){const u=Date.now(),c=r[s];o||(o=u),n[a]=i,r[a]=u;let l=s,d=0;for(;l!==a;)d+=n[l++],l%=t;if(a=(a+1)%t,a===s&&(s=(s+1)%t),u-o<e)return;const h=c&&u-c;return h?Math.round(1e3*d/h):void 0}}(50,250);return function(t,e){let n,r,o=0,a=1e3/e;const s=(e,a=Date.now())=>{o=a,n=null,r&&(clearTimeout(r),r=null),t.apply(null,e)};return[(...t)=>{const e=Date.now(),i=e-o;i>=a?s(t,e):(n=t,r||(r=setTimeout((()=>{r=null,s(n)}),a-i)))},()=>n&&s(n)]}((n=>{const a=n.loaded,s=n.lengthComputable?n.total:void 0,i=a-r,u=o(i);r=a;t({loaded:a,total:s,progress:s?a/s:void 0,bytes:i,rate:u||void 0,estimated:u&&s&&a<=s?(s-a)/u:void 0,event:n,lengthComputable:null!=s,[e?"download":"upload"]:!0})}),n)},St=(t,e)=>{const n=null!=t;return[r=>e[0]({lengthComputable:n,total:t,loaded:r}),e[1]]},Et=t=>(...e)=>Y.asap((()=>t(...e))),Ot=ct.hasStandardBrowserEnv?((t,e)=>n=>(n=new URL(n,ct.origin),t.protocol===n.protocol&&t.host===n.host&&(e||t.port===n.port)))(new URL(ct.origin),ct.navigator&&/(msie|trident)/i.test(ct.navigator.userAgent)):()=>!0,kt=ct.hasStandardBrowserEnv?{write(t,e,n,r,o,a){const s=[t+"="+encodeURIComponent(e)];Y.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),Y.isString(r)&&s.push("path="+r),Y.isString(o)&&s.push("domain="+o),!0===a&&s.push("secure"),document.cookie=s.join("; ")},read(t){const e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Mt(t,e,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e);return t&&(r||0==n)?function(t,e){return e?t.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):t}(t,e):e}const Dt=t=>t instanceof gt?{...t}:t;function Nt(t,e){e=e||{};const n={};function r(t,e,n,r){return Y.isPlainObject(t)&&Y.isPlainObject(e)?Y.merge.call({caseless:r},t,e):Y.isPlainObject(e)?Y.merge({},e):Y.isArray(e)?e.slice():e}function o(t,e,n,o){return Y.isUndefined(e)?Y.isUndefined(t)?void 0:r(void 0,t,0,o):r(t,e,0,o)}function a(t,e){if(!Y.isUndefined(e))return r(void 0,e)}function s(t,e){return Y.isUndefined(e)?Y.isUndefined(t)?void 0:r(void 0,t):r(void 0,e)}function i(n,o,a){return a in e?r(n,o):a in t?r(void 0,n):void 0}const u={url:a,method:a,data:a,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:i,headers:(t,e,n)=>o(Dt(t),Dt(e),0,!0)};return Y.forEach(Object.keys(Object.assign({},t,e)),(function(r){const a=u[r]||o,s=a(t[r],e[r],r);Y.isUndefined(s)&&a!==i||(n[r]=s)})),n}const Rt=t=>{const e=Nt({},t);let n,{data:r,withXSRFToken:o,xsrfHeaderName:a,xsrfCookieName:s,headers:i,auth:u}=e;if(e.headers=i=gt.from(i),e.url=tt(Mt(e.baseURL,e.url,e.allowAbsoluteUrls),t.params,t.paramsSerializer),u&&i.set("Authorization","Basic "+btoa((u.username||"")+":"+(u.password?unescape(encodeURIComponent(u.password)):""))),Y.isFormData(r))if(ct.hasStandardBrowserEnv||ct.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if(!1!==(n=i.getContentType())){const[t,...e]=n?n.split(";").map((t=>t.trim())).filter(Boolean):[];i.setContentType([t||"multipart/form-data",...e].join("; "))}if(ct.hasStandardBrowserEnv&&(o&&Y.isFunction(o)&&(o=o(e)),o||!1!==o&&Ot(e.url))){const t=a&&s&&kt.read(s);t&&i.set(a,t)}return e},Pt="undefined"!=typeof XMLHttpRequest&&function(t){return new Promise((function(e,n){const r=Rt(t);let o=r.data;const a=gt.from(r.headers).normalize();let s,i,u,c,l,{responseType:d,onUploadProgress:h,onDownloadProgress:f}=r;function m(){c&&c(),l&&l(),r.cancelToken&&r.cancelToken.unsubscribe(s),r.signal&&r.signal.removeEventListener("abort",s)}let p=new XMLHttpRequest;function w(){if(!p)return;const r=gt.from("getAllResponseHeaders"in p&&p.getAllResponseHeaders());Tt((function(t){e(t),m()}),(function(t){n(t),m()}),{data:d&&"text"!==d&&"json"!==d?p.response:p.responseText,status:p.status,statusText:p.statusText,headers:r,config:t,request:p}),p=null}p.open(r.method.toUpperCase(),r.url,!0),p.timeout=r.timeout,"onloadend"in p?p.onloadend=w:p.onreadystatechange=function(){p&&4===p.readyState&&(0!==p.status||p.responseURL&&0===p.responseURL.indexOf("file:"))&&setTimeout(w)},p.onabort=function(){p&&(n(new W("Request aborted",W.ECONNABORTED,t,p)),p=null)},p.onerror=function(){n(new W("Network Error",W.ERR_NETWORK,t,p)),p=null},p.ontimeout=function(){let e=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const o=r.transitional||nt;r.timeoutErrorMessage&&(e=r.timeoutErrorMessage),n(new W(e,o.clarifyTimeoutError?W.ETIMEDOUT:W.ECONNABORTED,t,p)),p=null},void 0===o&&a.setContentType(null),"setRequestHeader"in p&&Y.forEach(a.toJSON(),(function(t,e){p.setRequestHeader(e,t)})),Y.isUndefined(r.withCredentials)||(p.withCredentials=!!r.withCredentials),d&&"json"!==d&&(p.responseType=r.responseType),f&&([u,l]=vt(f,!0),p.addEventListener("progress",u)),h&&p.upload&&([i,c]=vt(h),p.upload.addEventListener("progress",i),p.upload.addEventListener("loadend",c)),(r.cancelToken||r.signal)&&(s=e=>{p&&(n(!e||e.type?new xt(null,t,p):e),p.abort(),p=null)},r.cancelToken&&r.cancelToken.subscribe(s),r.signal&&(r.signal.aborted?s():r.signal.addEventListener("abort",s)));const g=function(t){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return e&&e[1]||""}(r.url);g&&-1===ct.protocols.indexOf(g)?n(new W("Unsupported protocol "+g+":",W.ERR_BAD_REQUEST,t)):p.send(o||null)}))},Ct=(t,e)=>{const{length:n}=t=t?t.filter(Boolean):[];if(e||n){let n,r=new AbortController;const o=function(t){if(!n){n=!0,s();const e=t instanceof Error?t:this.reason;r.abort(e instanceof W?e:new xt(e instanceof Error?e.message:e))}};let a=e&&setTimeout((()=>{a=null,o(new W(`timeout ${e} of ms exceeded`,W.ETIMEDOUT))}),e);const s=()=>{t&&(a&&clearTimeout(a),a=null,t.forEach((t=>{t.unsubscribe?t.unsubscribe(o):t.removeEventListener("abort",o)})),t=null)};t.forEach((t=>t.addEventListener("abort",o)));const{signal:i}=r;return i.unsubscribe=()=>Y.asap(s),i}},At=function*(t,e){let n=t.byteLength;if(n<e)return void(yield t);let r,o=0;for(;o<n;)r=o+e,yield t.slice(o,r),o=r},Ft=async function*(t){if(t[Symbol.asyncIterator])return void(yield*t);const e=t.getReader();try{for(;;){const{done:t,value:n}=await e.read();if(t)break;yield n}}finally{await e.cancel()}},jt=(t,e,n,r)=>{const o=async function*(t,e){for await(const n of Ft(t))yield*At(n,e)}(t,e);let a,s=0,i=t=>{a||(a=!0,r&&r(t))};return new ReadableStream({async pull(t){try{const{done:e,value:r}=await o.next();if(e)return i(),void t.close();let a=r.byteLength;if(n){let t=s+=a;n(t)}t.enqueue(new Uint8Array(r))}catch(e){throw i(e),e}},cancel:t=>(i(t),o.return())},{highWaterMark:2})},Lt="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,qt=Lt&&"function"==typeof ReadableStream,Ht=Lt&&("function"==typeof TextEncoder?(t=>e=>t.encode(e))(new TextEncoder):async t=>new Uint8Array(await new Response(t).arrayBuffer())),Bt=(t,...e)=>{try{return!!t(...e)}catch(n){return!1}},Ut=qt&&Bt((()=>{let t=!1;const e=new Request(ct.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!e})),Yt=qt&&Bt((()=>Y.isReadableStream(new Response("").body))),Wt={stream:Yt&&(t=>t.body)};var _t;Lt&&(_t=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((t=>{!Wt[t]&&(Wt[t]=Y.isFunction(_t[t])?e=>e[t]():(e,n)=>{throw new W(`Response type '${t}' is not supported`,W.ERR_NOT_SUPPORT,n)})})));const It=async(t,e)=>{const n=Y.toFiniteNumber(t.getContentLength());return null==n?(async t=>{if(null==t)return 0;if(Y.isBlob(t))return t.size;if(Y.isSpecCompliantForm(t)){const e=new Request(ct.origin,{method:"POST",body:t});return(await e.arrayBuffer()).byteLength}return Y.isArrayBufferView(t)||Y.isArrayBuffer(t)?t.byteLength:(Y.isURLSearchParams(t)&&(t+=""),Y.isString(t)?(await Ht(t)).byteLength:void 0)})(e):n},zt={http:null,xhr:Pt,fetch:Lt&&(async t=>{let{url:e,method:n,data:r,signal:o,cancelToken:a,timeout:s,onDownloadProgress:i,onUploadProgress:u,responseType:c,headers:l,withCredentials:d="same-origin",fetchOptions:h}=Rt(t);c=c?(c+"").toLowerCase():"text";let f,m=Ct([o,a&&a.toAbortSignal()],s);const p=m&&m.unsubscribe&&(()=>{m.unsubscribe()});let w;try{if(u&&Ut&&"get"!==n&&"head"!==n&&0!==(w=await It(l,r))){let t,n=new Request(e,{method:"POST",body:r,duplex:"half"});if(Y.isFormData(r)&&(t=n.headers.get("content-type"))&&l.setContentType(t),n.body){const[t,e]=St(w,vt(Et(u)));r=jt(n.body,65536,t,e)}}Y.isString(d)||(d=d?"include":"omit");const o="credentials"in Request.prototype;f=new Request(e,{...h,signal:m,method:n.toUpperCase(),headers:l.normalize().toJSON(),body:r,duplex:"half",credentials:o?d:void 0});let a=await fetch(f);const s=Yt&&("stream"===c||"response"===c);if(Yt&&(i||s&&p)){const t={};["status","statusText","headers"].forEach((e=>{t[e]=a[e]}));const e=Y.toFiniteNumber(a.headers.get("content-length")),[n,r]=i&&St(e,vt(Et(i),!0))||[];a=new Response(jt(a.body,65536,n,(()=>{r&&r(),p&&p()})),t)}c=c||"text";let g=await Wt[Y.findKey(Wt,c)||"text"](a,t);return!s&&p&&p(),await new Promise(((e,n)=>{Tt(e,n,{data:g,headers:gt.from(a.headers),status:a.status,statusText:a.statusText,config:t,request:f})}))}catch(g){if(p&&p(),g&&"TypeError"===g.name&&/fetch/i.test(g.message))throw Object.assign(new W("Network Error",W.ERR_NETWORK,t,f),{cause:g.cause||g});throw W.from(g,g&&g.code,t,f)}})};Y.forEach(zt,((t,e)=>{if(t){try{Object.defineProperty(t,"name",{value:e})}catch(n){}Object.defineProperty(t,"adapterName",{value:e})}}));const Qt=t=>`- ${t}`,Xt=t=>Y.isFunction(t)||null===t||!1===t,Gt=t=>{t=Y.isArray(t)?t:[t];const{length:e}=t;let n,r;const o={};for(let a=0;a<e;a++){let e;if(n=t[a],r=n,!Xt(n)&&(r=zt[(e=String(n)).toLowerCase()],void 0===r))throw new W(`Unknown adapter '${e}'`);if(r)break;o[e||"#"+a]=r}if(!r){const t=Object.entries(o).map((([t,e])=>`adapter ${t} `+(!1===e?"is not supported by the environment":"is not available in the build")));throw new W("There is no suitable adapter to dispatch the request "+(e?t.length>1?"since :\n"+t.map(Qt).join("\n"):" "+Qt(t[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r};function $t(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new xt(null,t)}function Jt(t){$t(t),t.headers=gt.from(t.headers),t.data=bt.call(t,t.transformRequest),-1!==["post","put","patch"].indexOf(t.method)&&t.headers.setContentType("application/x-www-form-urlencoded",!1);return Gt(t.adapter||dt.adapter)(t).then((function(e){return $t(t),e.data=bt.call(t,t.transformResponse,e),e.headers=gt.from(e.headers),e}),(function(e){return yt(e)||($t(t),e&&e.response&&(e.response.data=bt.call(t,t.transformResponse,e.response),e.response.headers=gt.from(e.response.headers))),Promise.reject(e)}))}const Kt="1.8.4",Vt={};["object","boolean","number","function","string","symbol"].forEach(((t,e)=>{Vt[t]=function(n){return typeof n===t||"a"+(e<1?"n ":" ")+t}}));const Zt={};Vt.transitional=function(t,e,n){function r(t,e){return"[Axios v1.8.4] Transitional option '"+t+"'"+e+(n?". "+n:"")}return(n,o,a)=>{if(!1===t)throw new W(r(o," has been removed"+(e?" in "+e:"")),W.ERR_DEPRECATED);return e&&!Zt[o]&&(Zt[o]=!0,console.warn(r(o," has been deprecated since v"+e+" and will be removed in the near future"))),!t||t(n,o,a)}},Vt.spelling=function(t){return(e,n)=>(console.warn(`${n} is likely a misspelling of ${t}`),!0)};const te={assertOptions:function(t,e,n){if("object"!=typeof t)throw new W("options must be an object",W.ERR_BAD_OPTION_VALUE);const r=Object.keys(t);let o=r.length;for(;o-- >0;){const a=r[o],s=e[a];if(s){const e=t[a],n=void 0===e||s(e,a,t);if(!0!==n)throw new W("option "+a+" must be "+n,W.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new W("Unknown option "+a,W.ERR_BAD_OPTION)}},validators:Vt},ee=te.validators;class ne{constructor(t){this.defaults=t,this.interceptors={request:new et,response:new et}}async request(t,e){try{return await this._request(t,e)}catch(n){if(n instanceof Error){let t={};Error.captureStackTrace?Error.captureStackTrace(t):t=new Error;const e=t.stack?t.stack.replace(/^.+\n/,""):"";try{n.stack?e&&!String(n.stack).endsWith(e.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+e):n.stack=e}catch(r){}}throw n}}_request(t,e){"string"==typeof t?(e=e||{}).url=t:e=t||{},e=Nt(this.defaults,e);const{transitional:n,paramsSerializer:r,headers:o}=e;void 0!==n&&te.assertOptions(n,{silentJSONParsing:ee.transitional(ee.boolean),forcedJSONParsing:ee.transitional(ee.boolean),clarifyTimeoutError:ee.transitional(ee.boolean)},!1),null!=r&&(Y.isFunction(r)?e.paramsSerializer={serialize:r}:te.assertOptions(r,{encode:ee.function,serialize:ee.function},!0)),void 0!==e.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?e.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:e.allowAbsoluteUrls=!0),te.assertOptions(e,{baseUrl:ee.spelling("baseURL"),withXsrfToken:ee.spelling("withXSRFToken")},!0),e.method=(e.method||this.defaults.method||"get").toLowerCase();let a=o&&Y.merge(o.common,o[e.method]);o&&Y.forEach(["delete","get","head","post","put","patch","common"],(t=>{delete o[t]})),e.headers=gt.concat(a,o);const s=[];let i=!0;this.interceptors.request.forEach((function(t){"function"==typeof t.runWhen&&!1===t.runWhen(e)||(i=i&&t.synchronous,s.unshift(t.fulfilled,t.rejected))}));const u=[];let c;this.interceptors.response.forEach((function(t){u.push(t.fulfilled,t.rejected)}));let l,d=0;if(!i){const t=[Jt.bind(this),void 0];for(t.unshift.apply(t,s),t.push.apply(t,u),l=t.length,c=Promise.resolve(e);d<l;)c=c.then(t[d++],t[d++]);return c}l=s.length;let h=e;for(d=0;d<l;){const t=s[d++],e=s[d++];try{h=t(h)}catch(f){e.call(this,f);break}}try{c=Jt.call(this,h)}catch(f){return Promise.reject(f)}for(d=0,l=u.length;d<l;)c=c.then(u[d++],u[d++]);return c}getUri(t){return tt(Mt((t=Nt(this.defaults,t)).baseURL,t.url,t.allowAbsoluteUrls),t.params,t.paramsSerializer)}}Y.forEach(["delete","get","head","options"],(function(t){ne.prototype[t]=function(e,n){return this.request(Nt(n||{},{method:t,url:e,data:(n||{}).data}))}})),Y.forEach(["post","put","patch"],(function(t){function e(e){return function(n,r,o){return this.request(Nt(o||{},{method:t,headers:e?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}ne.prototype[t]=e(),ne.prototype[t+"Form"]=e(!0)}));class re{constructor(t){if("function"!=typeof t)throw new TypeError("executor must be a function.");let e;this.promise=new Promise((function(t){e=t}));const n=this;this.promise.then((t=>{if(!n._listeners)return;let e=n._listeners.length;for(;e-- >0;)n._listeners[e](t);n._listeners=null})),this.promise.then=t=>{let e;const r=new Promise((t=>{n.subscribe(t),e=t})).then(t);return r.cancel=function(){n.unsubscribe(e)},r},t((function(t,r,o){n.reason||(n.reason=new xt(t,r,o),e(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){this.reason?t(this.reason):this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const e=this._listeners.indexOf(t);-1!==e&&this._listeners.splice(e,1)}toAbortSignal(){const t=new AbortController,e=e=>{t.abort(e)};return this.subscribe(e),t.signal.unsubscribe=()=>this.unsubscribe(e),t.signal}static source(){let t;return{token:new re((function(e){t=e})),cancel:t}}}const oe={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(oe).forEach((([t,e])=>{oe[e]=t}));const ae=function t(e){const r=new ne(e),o=n(ne.prototype.request,r);return Y.extend(o,ne.prototype,r,{allOwnKeys:!0}),Y.extend(o,r,null,{allOwnKeys:!0}),o.create=function(n){return t(Nt(e,n))},o}(dt);function se(t){const e=Object.prototype.toString.call(t);return t instanceof Date||"object"==typeof t&&"[object Date]"===e?new t.constructor(+t):"number"==typeof t||"[object Number]"===e||"string"==typeof t||"[object String]"===e?new Date(t):new Date(NaN)}function ie(t,e){return t instanceof Date?new t.constructor(e):new Date(e)}function ue(t,e){const n=se(t);return isNaN(e)?ie(t,NaN):e?(n.setDate(n.getDate()+e),n):n}function ce(t,e){const n=se(t);if(isNaN(e))return ie(t,NaN);if(!e)return n;const r=n.getDate(),o=ie(t,n.getTime());o.setMonth(n.getMonth()+e+1,0);return r>=o.getDate()?o:(n.setFullYear(o.getFullYear(),o.getMonth(),r),n)}function le(t,e){return ie(t,+se(t)+e)}ae.Axios=ne,ae.CanceledError=xt,ae.CancelToken=re,ae.isCancel=yt,ae.VERSION=Kt,ae.toFormData=$,ae.AxiosError=W,ae.Cancel=ae.CanceledError,ae.all=function(t){return Promise.all(t)},ae.spread=function(t){return function(e){return t.apply(null,e)}},ae.isAxiosError=function(t){return Y.isObject(t)&&!0===t.isAxiosError},ae.mergeConfig=Nt,ae.AxiosHeaders=gt,ae.formToJSON=t=>lt(Y.isHTMLForm(t)?new FormData(t):t),ae.getAdapter=Gt,ae.HttpStatusCode=oe,ae.default=ae;const de=6048e5,he=6e4,fe=36e5;function me(t,e){return le(t,e*fe)}let pe={};function we(){return pe}function ge(t,e){const n=we(),r=e?.weekStartsOn??e?.locale?.options?.weekStartsOn??n.weekStartsOn??n.locale?.options?.weekStartsOn??0,o=se(t),a=o.getDay(),s=(a<r?7:0)+a-r;return o.setDate(o.getDate()-s),o.setHours(0,0,0,0),o}function be(t){return ge(t,{weekStartsOn:1})}function ye(t){const e=se(t),n=e.getFullYear(),r=ie(t,0);r.setFullYear(n+1,0,4),r.setHours(0,0,0,0);const o=be(r),a=ie(t,0);a.setFullYear(n,0,4),a.setHours(0,0,0,0);const s=be(a);return e.getTime()>=o.getTime()?n+1:e.getTime()>=s.getTime()?n:n-1}function xe(t){const e=se(t);return e.setHours(0,0,0,0),e}function Te(t){const e=se(t),n=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return n.setUTCFullYear(e.getFullYear()),+t-+n}function ve(t,e){const n=xe(t),r=xe(e),o=+n-Te(n),a=+r-Te(r);return Math.round((o-a)/864e5)}function Se(t,e){return le(t,e*he)}function Ee(t,e){return ce(t,3*e)}function Oe(t,e){return le(t,1e3*e)}function ke(t,e){return ue(t,7*e)}function Me(t,e){return ce(t,12*e)}function De(t,e){const n=se(t),r=se(e),o=n.getTime()-r.getTime();return o<0?-1:o>0?1:o}function Ne(t){if(!(e=t,e instanceof Date||"object"==typeof e&&"[object Date]"===Object.prototype.toString.call(e)||"number"==typeof t))return!1;var e;const n=se(t);return!isNaN(Number(n))}function Re(t,e){const n=se(t),r=se(e),o=Pe(n,r),a=Math.abs(ve(n,r));n.setDate(n.getDate()-o*a);const s=o*(a-Number(Pe(n,r)===-o));return 0===s?0:s}function Pe(t,e){const n=t.getFullYear()-e.getFullYear()||t.getMonth()-e.getMonth()||t.getDate()-e.getDate()||t.getHours()-e.getHours()||t.getMinutes()-e.getMinutes()||t.getSeconds()-e.getSeconds()||t.getMilliseconds()-e.getMilliseconds();return n<0?-1:n>0?1:n}function Ce(t){return e=>{const n=(t?Math[t]:Math.trunc)(e);return 0===n?0:n}}function Ae(t,e){return+se(t)-+se(e)}function Fe(t,e,n){const r=Ae(t,e)/fe;return Ce(n?.roundingMethod)(r)}function je(t,e,n){const r=Ae(t,e)/he;return Ce(n?.roundingMethod)(r)}function Le(t){const e=se(t);return e.setHours(23,59,59,999),e}function qe(t){const e=se(t),n=e.getMonth();return e.setFullYear(e.getFullYear(),n+1,0),e.setHours(23,59,59,999),e}function He(t,e){const n=se(t),r=se(e),o=De(n,r),a=Math.abs(function(t,e){const n=se(t),r=se(e);return 12*(n.getFullYear()-r.getFullYear())+(n.getMonth()-r.getMonth())}(n,r));let s;if(a<1)s=0;else{1===n.getMonth()&&n.getDate()>27&&n.setDate(30),n.setMonth(n.getMonth()-o*a);let e=De(n,r)===-o;(function(t){const e=se(t);return+Le(e)===+qe(e)})(se(t))&&1===a&&1===De(t,r)&&(e=!1),s=o*(a-Number(e))}return 0===s?0:s}function Be(t,e,n){const r=He(t,e)/3;return Ce(n?.roundingMethod)(r)}function Ue(t,e,n){const r=Ae(t,e)/1e3;return Ce(n?.roundingMethod)(r)}function Ye(t,e,n){const r=Re(t,e)/7;return Ce(n?.roundingMethod)(r)}function We(t,e){const n=se(t),r=se(e),o=De(n,r),a=Math.abs(function(t,e){const n=se(t),r=se(e);return n.getFullYear()-r.getFullYear()}(n,r));n.setFullYear(1584),r.setFullYear(1584);const s=o*(a-+(De(n,r)===-o));return 0===s?0:s}function _e(t){const e=se(t);return e.setSeconds(0,0),e}function Ie(t){const e=se(t),n=e.getMonth(),r=n-n%3;return e.setMonth(r,1),e.setHours(0,0,0,0),e}function ze(t){const e=se(t);return e.setDate(1),e.setHours(0,0,0,0),e}function Qe(t){const e=se(t),n=e.getFullYear();return e.setFullYear(n+1,0,0),e.setHours(23,59,59,999),e}function Xe(t){const e=se(t),n=ie(t,0);return n.setFullYear(e.getFullYear(),0,1),n.setHours(0,0,0,0),n}function Ge(t){const e=se(t);return e.setMinutes(59,59,999),e}function $e(t,e){const n=we(),r=n.weekStartsOn??n.locale?.options?.weekStartsOn??0,o=se(t),a=o.getDay(),s=6+(a<r?-7:0)-(a-r);return o.setDate(o.getDate()+s),o.setHours(23,59,59,999),o}function Je(t){const e=se(t);return e.setSeconds(59,999),e}function Ke(t){const e=se(t),n=e.getMonth(),r=n-n%3+3;return e.setMonth(r,0),e.setHours(23,59,59,999),e}function Ve(t){const e=se(t);return e.setMilliseconds(999),e}const Ze={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function tn(t){return(e={})=>{const n=e.width?String(e.width):t.defaultWidth;return t.formats[n]||t.formats[t.defaultWidth]}}const en={date:tn({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:tn({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:tn({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},nn={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function rn(t){return(e,n)=>{let r;if("formatting"===(n?.context?String(n.context):"standalone")&&t.formattingValues){const e=t.defaultFormattingWidth||t.defaultWidth,o=n?.width?String(n.width):e;r=t.formattingValues[o]||t.formattingValues[e]}else{const e=t.defaultWidth,o=n?.width?String(n.width):t.defaultWidth;r=t.values[o]||t.values[e]}return r[t.argumentCallback?t.argumentCallback(e):e]}}function on(t){return(e,n={})=>{const r=n.width,o=r&&t.matchPatterns[r]||t.matchPatterns[t.defaultMatchWidth],a=e.match(o);if(!a)return null;const s=a[0],i=r&&t.parsePatterns[r]||t.parsePatterns[t.defaultParseWidth],u=Array.isArray(i)?function(t,e){for(let n=0;n<t.length;n++)if(e(t[n]))return n;return}(i,(t=>t.test(s))):function(t,e){for(const n in t)if(Object.prototype.hasOwnProperty.call(t,n)&&e(t[n]))return n;return}(i,(t=>t.test(s)));let c;c=t.valueCallback?t.valueCallback(u):u,c=n.valueCallback?n.valueCallback(c):c;return{value:c,rest:e.slice(s.length)}}}var an;const sn={code:"en-US",formatDistance:(t,e,n)=>{let r;const o=Ze[t];return r="string"==typeof o?o:1===e?o.one:o.other.replace("{{count}}",e.toString()),n?.addSuffix?n.comparison&&n.comparison>0?"in "+r:r+" ago":r},formatLong:en,formatRelative:(t,e,n,r)=>nn[t],localize:{ordinalNumber:(t,e)=>{const n=Number(t),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:rn({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:rn({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:t=>t-1}),month:rn({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:rn({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:rn({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:(an={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:t=>parseInt(t,10)},(t,e={})=>{const n=t.match(an.matchPattern);if(!n)return null;const r=n[0],o=t.match(an.parsePattern);if(!o)return null;let a=an.valueCallback?an.valueCallback(o[0]):o[0];return a=e.valueCallback?e.valueCallback(a):a,{value:a,rest:t.slice(r.length)}}),era:on({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:on({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:t=>t+1}),month:on({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:on({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:on({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}};function un(t){const e=se(t),n=+be(e)-+function(t){const e=ye(t),n=ie(t,0);return n.setFullYear(e,0,4),n.setHours(0,0,0,0),be(n)}(e);return Math.round(n/de)+1}function cn(t,e){const n=se(t),r=n.getFullYear(),o=we(),a=e?.firstWeekContainsDate??e?.locale?.options?.firstWeekContainsDate??o.firstWeekContainsDate??o.locale?.options?.firstWeekContainsDate??1,s=ie(t,0);s.setFullYear(r+1,0,a),s.setHours(0,0,0,0);const i=ge(s,e),u=ie(t,0);u.setFullYear(r,0,a),u.setHours(0,0,0,0);const c=ge(u,e);return n.getTime()>=i.getTime()?r+1:n.getTime()>=c.getTime()?r:r-1}function ln(t,e){const n=se(t),r=+ge(n,e)-+function(t,e){const n=we(),r=e?.firstWeekContainsDate??e?.locale?.options?.firstWeekContainsDate??n.firstWeekContainsDate??n.locale?.options?.firstWeekContainsDate??1,o=cn(t,e),a=ie(t,0);return a.setFullYear(o,0,r),a.setHours(0,0,0,0),ge(a,e)}(n,e);return Math.round(r/de)+1}function dn(t,e){return(t<0?"-":"")+Math.abs(t).toString().padStart(e,"0")}const hn={y(t,e){const n=t.getFullYear(),r=n>0?n:1-n;return dn("yy"===e?r%100:r,e.length)},M(t,e){const n=t.getMonth();return"M"===e?String(n+1):dn(n+1,2)},d:(t,e)=>dn(t.getDate(),e.length),a(t,e){const n=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return"am"===n?"a.m.":"p.m."}},h:(t,e)=>dn(t.getHours()%12||12,e.length),H:(t,e)=>dn(t.getHours(),e.length),m:(t,e)=>dn(t.getMinutes(),e.length),s:(t,e)=>dn(t.getSeconds(),e.length),S(t,e){const n=e.length,r=t.getMilliseconds();return dn(Math.trunc(r*Math.pow(10,n-3)),e.length)}},fn="midnight",mn="noon",pn="morning",wn="afternoon",gn="evening",bn="night",yn={G:function(t,e,n){const r=t.getFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});default:return n.era(r,{width:"wide"})}},y:function(t,e,n){if("yo"===e){const e=t.getFullYear(),r=e>0?e:1-e;return n.ordinalNumber(r,{unit:"year"})}return hn.y(t,e)},Y:function(t,e,n,r){const o=cn(t,r),a=o>0?o:1-o;if("YY"===e){return dn(a%100,2)}return"Yo"===e?n.ordinalNumber(a,{unit:"year"}):dn(a,e.length)},R:function(t,e){return dn(ye(t),e.length)},u:function(t,e){return dn(t.getFullYear(),e.length)},Q:function(t,e,n){const r=Math.ceil((t.getMonth()+1)/3);switch(e){case"Q":return String(r);case"QQ":return dn(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(t,e,n){const r=Math.ceil((t.getMonth()+1)/3);switch(e){case"q":return String(r);case"qq":return dn(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(t,e,n){const r=t.getMonth();switch(e){case"M":case"MM":return hn.M(t,e);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(t,e,n){const r=t.getMonth();switch(e){case"L":return String(r+1);case"LL":return dn(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(t,e,n,r){const o=ln(t,r);return"wo"===e?n.ordinalNumber(o,{unit:"week"}):dn(o,e.length)},I:function(t,e,n){const r=un(t);return"Io"===e?n.ordinalNumber(r,{unit:"week"}):dn(r,e.length)},d:function(t,e,n){return"do"===e?n.ordinalNumber(t.getDate(),{unit:"date"}):hn.d(t,e)},D:function(t,e,n){const r=function(t){const e=se(t);return ve(e,Xe(e))+1}(t);return"Do"===e?n.ordinalNumber(r,{unit:"dayOfYear"}):dn(r,e.length)},E:function(t,e,n){const r=t.getDay();switch(e){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(t,e,n,r){const o=t.getDay(),a=(o-r.weekStartsOn+8)%7||7;switch(e){case"e":return String(a);case"ee":return dn(a,2);case"eo":return n.ordinalNumber(a,{unit:"day"});case"eee":return n.day(o,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(o,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(o,{width:"short",context:"formatting"});default:return n.day(o,{width:"wide",context:"formatting"})}},c:function(t,e,n,r){const o=t.getDay(),a=(o-r.weekStartsOn+8)%7||7;switch(e){case"c":return String(a);case"cc":return dn(a,e.length);case"co":return n.ordinalNumber(a,{unit:"day"});case"ccc":return n.day(o,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(o,{width:"narrow",context:"standalone"});case"cccccc":return n.day(o,{width:"short",context:"standalone"});default:return n.day(o,{width:"wide",context:"standalone"})}},i:function(t,e,n){const r=t.getDay(),o=0===r?7:r;switch(e){case"i":return String(o);case"ii":return dn(o,e.length);case"io":return n.ordinalNumber(o,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(t,e,n){const r=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(t,e,n){const r=t.getHours();let o;switch(o=12===r?mn:0===r?fn:r/12>=1?"pm":"am",e){case"b":case"bb":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(o,{width:"narrow",context:"formatting"});default:return n.dayPeriod(o,{width:"wide",context:"formatting"})}},B:function(t,e,n){const r=t.getHours();let o;switch(o=r>=17?gn:r>=12?wn:r>=4?pn:bn,e){case"B":case"BB":case"BBB":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(o,{width:"narrow",context:"formatting"});default:return n.dayPeriod(o,{width:"wide",context:"formatting"})}},h:function(t,e,n){if("ho"===e){let e=t.getHours()%12;return 0===e&&(e=12),n.ordinalNumber(e,{unit:"hour"})}return hn.h(t,e)},H:function(t,e,n){return"Ho"===e?n.ordinalNumber(t.getHours(),{unit:"hour"}):hn.H(t,e)},K:function(t,e,n){const r=t.getHours()%12;return"Ko"===e?n.ordinalNumber(r,{unit:"hour"}):dn(r,e.length)},k:function(t,e,n){let r=t.getHours();return 0===r&&(r=24),"ko"===e?n.ordinalNumber(r,{unit:"hour"}):dn(r,e.length)},m:function(t,e,n){return"mo"===e?n.ordinalNumber(t.getMinutes(),{unit:"minute"}):hn.m(t,e)},s:function(t,e,n){return"so"===e?n.ordinalNumber(t.getSeconds(),{unit:"second"}):hn.s(t,e)},S:function(t,e){return hn.S(t,e)},X:function(t,e,n){const r=t.getTimezoneOffset();if(0===r)return"Z";switch(e){case"X":return Tn(r);case"XXXX":case"XX":return vn(r);default:return vn(r,":")}},x:function(t,e,n){const r=t.getTimezoneOffset();switch(e){case"x":return Tn(r);case"xxxx":case"xx":return vn(r);default:return vn(r,":")}},O:function(t,e,n){const r=t.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+xn(r,":");default:return"GMT"+vn(r,":")}},z:function(t,e,n){const r=t.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+xn(r,":");default:return"GMT"+vn(r,":")}},t:function(t,e,n){return dn(Math.trunc(t.getTime()/1e3),e.length)},T:function(t,e,n){return dn(t.getTime(),e.length)}};function xn(t,e=""){const n=t>0?"-":"+",r=Math.abs(t),o=Math.trunc(r/60),a=r%60;return 0===a?n+String(o):n+String(o)+e+dn(a,2)}function Tn(t,e){if(t%60==0){return(t>0?"-":"+")+dn(Math.abs(t)/60,2)}return vn(t,e)}function vn(t,e=""){const n=t>0?"-":"+",r=Math.abs(t);return n+dn(Math.trunc(r/60),2)+e+dn(r%60,2)}const Sn=(t,e)=>{switch(t){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});default:return e.date({width:"full"})}},En=(t,e)=>{switch(t){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});default:return e.time({width:"full"})}},On={p:En,P:(t,e)=>{const n=t.match(/(P+)(p+)?/)||[],r=n[1],o=n[2];if(!o)return Sn(t,e);let a;switch(r){case"P":a=e.dateTime({width:"short"});break;case"PP":a=e.dateTime({width:"medium"});break;case"PPP":a=e.dateTime({width:"long"});break;default:a=e.dateTime({width:"full"})}return a.replace("{{date}}",Sn(r,e)).replace("{{time}}",En(o,e))}},kn=/^D+$/,Mn=/^Y+$/,Dn=["D","DD","YY","YYYY"];function Nn(t){return kn.test(t)}function Rn(t){return Mn.test(t)}function Pn(t,e,n){const r=function(t,e,n){const r="Y"===t[0]?"years":"days of the month";return`Use \`${t.toLowerCase()}\` instead of \`${t}\` (in \`${e}\`) for formatting ${r} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(t,e,n);if(console.warn(r),Dn.includes(t))throw new RangeError(r)}const Cn=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,An=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Fn=/^'([^]*?)'?$/,jn=/''/g,Ln=/[a-zA-Z]/;function qn(t,e,n){const r=we(),o=n?.locale??r.locale??sn,a=n?.firstWeekContainsDate??n?.locale?.options?.firstWeekContainsDate??r.firstWeekContainsDate??r.locale?.options?.firstWeekContainsDate??1,s=n?.weekStartsOn??n?.locale?.options?.weekStartsOn??r.weekStartsOn??r.locale?.options?.weekStartsOn??0,i=se(t);if(!Ne(i))throw new RangeError("Invalid time value");let u=e.match(An).map((t=>{const e=t[0];if("p"===e||"P"===e){return(0,On[e])(t,o.formatLong)}return t})).join("").match(Cn).map((t=>{if("''"===t)return{isToken:!1,value:"'"};const e=t[0];if("'"===e)return{isToken:!1,value:Hn(t)};if(yn[e])return{isToken:!0,value:t};if(e.match(Ln))throw new RangeError("Format string contains an unescaped latin alphabet character `"+e+"`");return{isToken:!1,value:t}}));o.localize.preprocessor&&(u=o.localize.preprocessor(i,u));const c={firstWeekContainsDate:a,weekStartsOn:s,locale:o};return u.map((r=>{if(!r.isToken)return r.value;const a=r.value;(!n?.useAdditionalWeekYearTokens&&Rn(a)||!n?.useAdditionalDayOfYearTokens&&Nn(a))&&Pn(a,e,String(t));return(0,yn[a[0]])(i,a,o.localize,c)})).join("")}function Hn(t){const e=t.match(Fn);return e?e[1].replace(jn,"'"):t}class Bn{constructor(){e(this,"subPriority",0)}validate(t,e){return!0}}class Un extends Bn{constructor(t,e,n,r,o){super(),this.value=t,this.validateValue=e,this.setValue=n,this.priority=r,o&&(this.subPriority=o)}validate(t,e){return this.validateValue(t,this.value,e)}set(t,e,n){return this.setValue(t,e,this.value,n)}}class Yn extends Bn{constructor(){super(...arguments),e(this,"priority",10),e(this,"subPriority",-1)}set(t,e){return e.timestampIsSet?t:ie(t,function(t,e){const n=e instanceof Date?ie(e,0):new e(0);return n.setFullYear(t.getFullYear(),t.getMonth(),t.getDate()),n.setHours(t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()),n}(t,Date))}}class Wn{run(t,e,n,r){const o=this.parse(t,e,n,r);return o?{setter:new Un(o.value,this.validate,this.set,this.priority,this.subPriority),rest:o.rest}:null}validate(t,e,n){return!0}}const _n=/^(1[0-2]|0?\d)/,In=/^(3[0-1]|[0-2]?\d)/,zn=/^(36[0-6]|3[0-5]\d|[0-2]?\d?\d)/,Qn=/^(5[0-3]|[0-4]?\d)/,Xn=/^(2[0-3]|[0-1]?\d)/,Gn=/^(2[0-4]|[0-1]?\d)/,$n=/^(1[0-1]|0?\d)/,Jn=/^(1[0-2]|0?\d)/,Kn=/^[0-5]?\d/,Vn=/^[0-5]?\d/,Zn=/^\d/,tr=/^\d{1,2}/,er=/^\d{1,3}/,nr=/^\d{1,4}/,rr=/^-?\d+/,or=/^-?\d/,ar=/^-?\d{1,2}/,sr=/^-?\d{1,3}/,ir=/^-?\d{1,4}/,ur=/^([+-])(\d{2})(\d{2})?|Z/,cr=/^([+-])(\d{2})(\d{2})|Z/,lr=/^([+-])(\d{2})(\d{2})((\d{2}))?|Z/,dr=/^([+-])(\d{2}):(\d{2})|Z/,hr=/^([+-])(\d{2}):(\d{2})(:(\d{2}))?|Z/;function fr(t,e){return t?{value:e(t.value),rest:t.rest}:t}function mr(t,e){const n=e.match(t);return n?{value:parseInt(n[0],10),rest:e.slice(n[0].length)}:null}function pr(t,e){const n=e.match(t);if(!n)return null;if("Z"===n[0])return{value:0,rest:e.slice(1)};const r="+"===n[1]?1:-1,o=n[2]?parseInt(n[2],10):0,a=n[3]?parseInt(n[3],10):0,s=n[5]?parseInt(n[5],10):0;return{value:r*(o*fe+a*he+1e3*s),rest:e.slice(n[0].length)}}function wr(t){return mr(rr,t)}function gr(t,e){switch(t){case 1:return mr(Zn,e);case 2:return mr(tr,e);case 3:return mr(er,e);case 4:return mr(nr,e);default:return mr(new RegExp("^\\d{1,"+t+"}"),e)}}function br(t,e){switch(t){case 1:return mr(or,e);case 2:return mr(ar,e);case 3:return mr(sr,e);case 4:return mr(ir,e);default:return mr(new RegExp("^-?\\d{1,"+t+"}"),e)}}function yr(t){switch(t){case"morning":return 4;case"evening":return 17;case"pm":case"noon":case"afternoon":return 12;default:return 0}}function xr(t,e){const n=e>0,r=n?e:1-e;let o;if(r<=50)o=t||100;else{const e=r+50;o=t+100*Math.trunc(e/100)-(t>=e%100?100:0)}return n?o:1-o}function Tr(t){return t%400==0||t%4==0&&t%100!=0}const vr=[31,28,31,30,31,30,31,31,30,31,30,31],Sr=[31,29,31,30,31,30,31,31,30,31,30,31];function Er(t,e,n){const r=we(),o=n?.weekStartsOn??n?.locale?.options?.weekStartsOn??r.weekStartsOn??r.locale?.options?.weekStartsOn??0,a=se(t),s=a.getDay(),i=7-o;return ue(a,e<0||e>6?e-(s+i)%7:((e%7+7)%7+i)%7-(s+i)%7)}function Or(t,e){const n=se(t),r=function(t){let e=se(t).getDay();return 0===e&&(e=7),e}(n);return ue(n,e-r)}const kr={G:new class extends Wn{constructor(){super(...arguments),e(this,"priority",140),e(this,"incompatibleTokens",["R","u","t","T"])}parse(t,e,n){switch(e){case"G":case"GG":case"GGG":return n.era(t,{width:"abbreviated"})||n.era(t,{width:"narrow"});case"GGGGG":return n.era(t,{width:"narrow"});default:return n.era(t,{width:"wide"})||n.era(t,{width:"abbreviated"})||n.era(t,{width:"narrow"})}}set(t,e,n){return e.era=n,t.setFullYear(n,0,1),t.setHours(0,0,0,0),t}},y:new class extends Wn{constructor(){super(...arguments),e(this,"priority",130),e(this,"incompatibleTokens",["Y","R","u","w","I","i","e","c","t","T"])}parse(t,e,n){const r=t=>({year:t,isTwoDigitYear:"yy"===e});switch(e){case"y":return fr(gr(4,t),r);case"yo":return fr(n.ordinalNumber(t,{unit:"year"}),r);default:return fr(gr(e.length,t),r)}}validate(t,e){return e.isTwoDigitYear||e.year>0}set(t,e,n){const r=t.getFullYear();if(n.isTwoDigitYear){const e=xr(n.year,r);return t.setFullYear(e,0,1),t.setHours(0,0,0,0),t}const o="era"in e&&1!==e.era?1-n.year:n.year;return t.setFullYear(o,0,1),t.setHours(0,0,0,0),t}},Y:new class extends Wn{constructor(){super(...arguments),e(this,"priority",130),e(this,"incompatibleTokens",["y","R","u","Q","q","M","L","I","d","D","i","t","T"])}parse(t,e,n){const r=t=>({year:t,isTwoDigitYear:"YY"===e});switch(e){case"Y":return fr(gr(4,t),r);case"Yo":return fr(n.ordinalNumber(t,{unit:"year"}),r);default:return fr(gr(e.length,t),r)}}validate(t,e){return e.isTwoDigitYear||e.year>0}set(t,e,n,r){const o=cn(t,r);if(n.isTwoDigitYear){const e=xr(n.year,o);return t.setFullYear(e,0,r.firstWeekContainsDate),t.setHours(0,0,0,0),ge(t,r)}const a="era"in e&&1!==e.era?1-n.year:n.year;return t.setFullYear(a,0,r.firstWeekContainsDate),t.setHours(0,0,0,0),ge(t,r)}},R:new class extends Wn{constructor(){super(...arguments),e(this,"priority",130),e(this,"incompatibleTokens",["G","y","Y","u","Q","q","M","L","w","d","D","e","c","t","T"])}parse(t,e){return br("R"===e?4:e.length,t)}set(t,e,n){const r=ie(t,0);return r.setFullYear(n,0,4),r.setHours(0,0,0,0),be(r)}},u:new class extends Wn{constructor(){super(...arguments),e(this,"priority",130),e(this,"incompatibleTokens",["G","y","Y","R","w","I","i","e","c","t","T"])}parse(t,e){return br("u"===e?4:e.length,t)}set(t,e,n){return t.setFullYear(n,0,1),t.setHours(0,0,0,0),t}},Q:new class extends Wn{constructor(){super(...arguments),e(this,"priority",120),e(this,"incompatibleTokens",["Y","R","q","M","L","w","I","d","D","i","e","c","t","T"])}parse(t,e,n){switch(e){case"Q":case"QQ":return gr(e.length,t);case"Qo":return n.ordinalNumber(t,{unit:"quarter"});case"QQQ":return n.quarter(t,{width:"abbreviated",context:"formatting"})||n.quarter(t,{width:"narrow",context:"formatting"});case"QQQQQ":return n.quarter(t,{width:"narrow",context:"formatting"});default:return n.quarter(t,{width:"wide",context:"formatting"})||n.quarter(t,{width:"abbreviated",context:"formatting"})||n.quarter(t,{width:"narrow",context:"formatting"})}}validate(t,e){return e>=1&&e<=4}set(t,e,n){return t.setMonth(3*(n-1),1),t.setHours(0,0,0,0),t}},q:new class extends Wn{constructor(){super(...arguments),e(this,"priority",120),e(this,"incompatibleTokens",["Y","R","Q","M","L","w","I","d","D","i","e","c","t","T"])}parse(t,e,n){switch(e){case"q":case"qq":return gr(e.length,t);case"qo":return n.ordinalNumber(t,{unit:"quarter"});case"qqq":return n.quarter(t,{width:"abbreviated",context:"standalone"})||n.quarter(t,{width:"narrow",context:"standalone"});case"qqqqq":return n.quarter(t,{width:"narrow",context:"standalone"});default:return n.quarter(t,{width:"wide",context:"standalone"})||n.quarter(t,{width:"abbreviated",context:"standalone"})||n.quarter(t,{width:"narrow",context:"standalone"})}}validate(t,e){return e>=1&&e<=4}set(t,e,n){return t.setMonth(3*(n-1),1),t.setHours(0,0,0,0),t}},M:new class extends Wn{constructor(){super(...arguments),e(this,"incompatibleTokens",["Y","R","q","Q","L","w","I","D","i","e","c","t","T"]),e(this,"priority",110)}parse(t,e,n){const r=t=>t-1;switch(e){case"M":return fr(mr(_n,t),r);case"MM":return fr(gr(2,t),r);case"Mo":return fr(n.ordinalNumber(t,{unit:"month"}),r);case"MMM":return n.month(t,{width:"abbreviated",context:"formatting"})||n.month(t,{width:"narrow",context:"formatting"});case"MMMMM":return n.month(t,{width:"narrow",context:"formatting"});default:return n.month(t,{width:"wide",context:"formatting"})||n.month(t,{width:"abbreviated",context:"formatting"})||n.month(t,{width:"narrow",context:"formatting"})}}validate(t,e){return e>=0&&e<=11}set(t,e,n){return t.setMonth(n,1),t.setHours(0,0,0,0),t}},L:new class extends Wn{constructor(){super(...arguments),e(this,"priority",110),e(this,"incompatibleTokens",["Y","R","q","Q","M","w","I","D","i","e","c","t","T"])}parse(t,e,n){const r=t=>t-1;switch(e){case"L":return fr(mr(_n,t),r);case"LL":return fr(gr(2,t),r);case"Lo":return fr(n.ordinalNumber(t,{unit:"month"}),r);case"LLL":return n.month(t,{width:"abbreviated",context:"standalone"})||n.month(t,{width:"narrow",context:"standalone"});case"LLLLL":return n.month(t,{width:"narrow",context:"standalone"});default:return n.month(t,{width:"wide",context:"standalone"})||n.month(t,{width:"abbreviated",context:"standalone"})||n.month(t,{width:"narrow",context:"standalone"})}}validate(t,e){return e>=0&&e<=11}set(t,e,n){return t.setMonth(n,1),t.setHours(0,0,0,0),t}},w:new class extends Wn{constructor(){super(...arguments),e(this,"priority",100),e(this,"incompatibleTokens",["y","R","u","q","Q","M","L","I","d","D","i","t","T"])}parse(t,e,n){switch(e){case"w":return mr(Qn,t);case"wo":return n.ordinalNumber(t,{unit:"week"});default:return gr(e.length,t)}}validate(t,e){return e>=1&&e<=53}set(t,e,n,r){return ge(function(t,e,n){const r=se(t),o=ln(r,n)-e;return r.setDate(r.getDate()-7*o),r}(t,n,r),r)}},I:new class extends Wn{constructor(){super(...arguments),e(this,"priority",100),e(this,"incompatibleTokens",["y","Y","u","q","Q","M","L","w","d","D","e","c","t","T"])}parse(t,e,n){switch(e){case"I":return mr(Qn,t);case"Io":return n.ordinalNumber(t,{unit:"week"});default:return gr(e.length,t)}}validate(t,e){return e>=1&&e<=53}set(t,e,n){return be(function(t,e){const n=se(t),r=un(n)-e;return n.setDate(n.getDate()-7*r),n}(t,n))}},d:new class extends Wn{constructor(){super(...arguments),e(this,"priority",90),e(this,"subPriority",1),e(this,"incompatibleTokens",["Y","R","q","Q","w","I","D","i","e","c","t","T"])}parse(t,e,n){switch(e){case"d":return mr(In,t);case"do":return n.ordinalNumber(t,{unit:"date"});default:return gr(e.length,t)}}validate(t,e){const n=Tr(t.getFullYear()),r=t.getMonth();return n?e>=1&&e<=Sr[r]:e>=1&&e<=vr[r]}set(t,e,n){return t.setDate(n),t.setHours(0,0,0,0),t}},D:new class extends Wn{constructor(){super(...arguments),e(this,"priority",90),e(this,"subpriority",1),e(this,"incompatibleTokens",["Y","R","q","Q","M","L","w","I","d","E","i","e","c","t","T"])}parse(t,e,n){switch(e){case"D":case"DD":return mr(zn,t);case"Do":return n.ordinalNumber(t,{unit:"date"});default:return gr(e.length,t)}}validate(t,e){return Tr(t.getFullYear())?e>=1&&e<=366:e>=1&&e<=365}set(t,e,n){return t.setMonth(0,n),t.setHours(0,0,0,0),t}},E:new class extends Wn{constructor(){super(...arguments),e(this,"priority",90),e(this,"incompatibleTokens",["D","i","e","c","t","T"])}parse(t,e,n){switch(e){case"E":case"EE":case"EEE":return n.day(t,{width:"abbreviated",context:"formatting"})||n.day(t,{width:"short",context:"formatting"})||n.day(t,{width:"narrow",context:"formatting"});case"EEEEE":return n.day(t,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(t,{width:"short",context:"formatting"})||n.day(t,{width:"narrow",context:"formatting"});default:return n.day(t,{width:"wide",context:"formatting"})||n.day(t,{width:"abbreviated",context:"formatting"})||n.day(t,{width:"short",context:"formatting"})||n.day(t,{width:"narrow",context:"formatting"})}}validate(t,e){return e>=0&&e<=6}set(t,e,n,r){return(t=Er(t,n,r)).setHours(0,0,0,0),t}},e:new class extends Wn{constructor(){super(...arguments),e(this,"priority",90),e(this,"incompatibleTokens",["y","R","u","q","Q","M","L","I","d","D","E","i","c","t","T"])}parse(t,e,n,r){const o=t=>{const e=7*Math.floor((t-1)/7);return(t+r.weekStartsOn+6)%7+e};switch(e){case"e":case"ee":return fr(gr(e.length,t),o);case"eo":return fr(n.ordinalNumber(t,{unit:"day"}),o);case"eee":return n.day(t,{width:"abbreviated",context:"formatting"})||n.day(t,{width:"short",context:"formatting"})||n.day(t,{width:"narrow",context:"formatting"});case"eeeee":return n.day(t,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(t,{width:"short",context:"formatting"})||n.day(t,{width:"narrow",context:"formatting"});default:return n.day(t,{width:"wide",context:"formatting"})||n.day(t,{width:"abbreviated",context:"formatting"})||n.day(t,{width:"short",context:"formatting"})||n.day(t,{width:"narrow",context:"formatting"})}}validate(t,e){return e>=0&&e<=6}set(t,e,n,r){return(t=Er(t,n,r)).setHours(0,0,0,0),t}},c:new class extends Wn{constructor(){super(...arguments),e(this,"priority",90),e(this,"incompatibleTokens",["y","R","u","q","Q","M","L","I","d","D","E","i","e","t","T"])}parse(t,e,n,r){const o=t=>{const e=7*Math.floor((t-1)/7);return(t+r.weekStartsOn+6)%7+e};switch(e){case"c":case"cc":return fr(gr(e.length,t),o);case"co":return fr(n.ordinalNumber(t,{unit:"day"}),o);case"ccc":return n.day(t,{width:"abbreviated",context:"standalone"})||n.day(t,{width:"short",context:"standalone"})||n.day(t,{width:"narrow",context:"standalone"});case"ccccc":return n.day(t,{width:"narrow",context:"standalone"});case"cccccc":return n.day(t,{width:"short",context:"standalone"})||n.day(t,{width:"narrow",context:"standalone"});default:return n.day(t,{width:"wide",context:"standalone"})||n.day(t,{width:"abbreviated",context:"standalone"})||n.day(t,{width:"short",context:"standalone"})||n.day(t,{width:"narrow",context:"standalone"})}}validate(t,e){return e>=0&&e<=6}set(t,e,n,r){return(t=Er(t,n,r)).setHours(0,0,0,0),t}},i:new class extends Wn{constructor(){super(...arguments),e(this,"priority",90),e(this,"incompatibleTokens",["y","Y","u","q","Q","M","L","w","d","D","E","e","c","t","T"])}parse(t,e,n){const r=t=>0===t?7:t;switch(e){case"i":case"ii":return gr(e.length,t);case"io":return n.ordinalNumber(t,{unit:"day"});case"iii":return fr(n.day(t,{width:"abbreviated",context:"formatting"})||n.day(t,{width:"short",context:"formatting"})||n.day(t,{width:"narrow",context:"formatting"}),r);case"iiiii":return fr(n.day(t,{width:"narrow",context:"formatting"}),r);case"iiiiii":return fr(n.day(t,{width:"short",context:"formatting"})||n.day(t,{width:"narrow",context:"formatting"}),r);default:return fr(n.day(t,{width:"wide",context:"formatting"})||n.day(t,{width:"abbreviated",context:"formatting"})||n.day(t,{width:"short",context:"formatting"})||n.day(t,{width:"narrow",context:"formatting"}),r)}}validate(t,e){return e>=1&&e<=7}set(t,e,n){return(t=Or(t,n)).setHours(0,0,0,0),t}},a:new class extends Wn{constructor(){super(...arguments),e(this,"priority",80),e(this,"incompatibleTokens",["b","B","H","k","t","T"])}parse(t,e,n){switch(e){case"a":case"aa":case"aaa":return n.dayPeriod(t,{width:"abbreviated",context:"formatting"})||n.dayPeriod(t,{width:"narrow",context:"formatting"});case"aaaaa":return n.dayPeriod(t,{width:"narrow",context:"formatting"});default:return n.dayPeriod(t,{width:"wide",context:"formatting"})||n.dayPeriod(t,{width:"abbreviated",context:"formatting"})||n.dayPeriod(t,{width:"narrow",context:"formatting"})}}set(t,e,n){return t.setHours(yr(n),0,0,0),t}},b:new class extends Wn{constructor(){super(...arguments),e(this,"priority",80),e(this,"incompatibleTokens",["a","B","H","k","t","T"])}parse(t,e,n){switch(e){case"b":case"bb":case"bbb":return n.dayPeriod(t,{width:"abbreviated",context:"formatting"})||n.dayPeriod(t,{width:"narrow",context:"formatting"});case"bbbbb":return n.dayPeriod(t,{width:"narrow",context:"formatting"});default:return n.dayPeriod(t,{width:"wide",context:"formatting"})||n.dayPeriod(t,{width:"abbreviated",context:"formatting"})||n.dayPeriod(t,{width:"narrow",context:"formatting"})}}set(t,e,n){return t.setHours(yr(n),0,0,0),t}},B:new class extends Wn{constructor(){super(...arguments),e(this,"priority",80),e(this,"incompatibleTokens",["a","b","t","T"])}parse(t,e,n){switch(e){case"B":case"BB":case"BBB":return n.dayPeriod(t,{width:"abbreviated",context:"formatting"})||n.dayPeriod(t,{width:"narrow",context:"formatting"});case"BBBBB":return n.dayPeriod(t,{width:"narrow",context:"formatting"});default:return n.dayPeriod(t,{width:"wide",context:"formatting"})||n.dayPeriod(t,{width:"abbreviated",context:"formatting"})||n.dayPeriod(t,{width:"narrow",context:"formatting"})}}set(t,e,n){return t.setHours(yr(n),0,0,0),t}},h:new class extends Wn{constructor(){super(...arguments),e(this,"priority",70),e(this,"incompatibleTokens",["H","K","k","t","T"])}parse(t,e,n){switch(e){case"h":return mr(Jn,t);case"ho":return n.ordinalNumber(t,{unit:"hour"});default:return gr(e.length,t)}}validate(t,e){return e>=1&&e<=12}set(t,e,n){const r=t.getHours()>=12;return r&&n<12?t.setHours(n+12,0,0,0):r||12!==n?t.setHours(n,0,0,0):t.setHours(0,0,0,0),t}},H:new class extends Wn{constructor(){super(...arguments),e(this,"priority",70),e(this,"incompatibleTokens",["a","b","h","K","k","t","T"])}parse(t,e,n){switch(e){case"H":return mr(Xn,t);case"Ho":return n.ordinalNumber(t,{unit:"hour"});default:return gr(e.length,t)}}validate(t,e){return e>=0&&e<=23}set(t,e,n){return t.setHours(n,0,0,0),t}},K:new class extends Wn{constructor(){super(...arguments),e(this,"priority",70),e(this,"incompatibleTokens",["h","H","k","t","T"])}parse(t,e,n){switch(e){case"K":return mr($n,t);case"Ko":return n.ordinalNumber(t,{unit:"hour"});default:return gr(e.length,t)}}validate(t,e){return e>=0&&e<=11}set(t,e,n){return t.getHours()>=12&&n<12?t.setHours(n+12,0,0,0):t.setHours(n,0,0,0),t}},k:new class extends Wn{constructor(){super(...arguments),e(this,"priority",70),e(this,"incompatibleTokens",["a","b","h","H","K","t","T"])}parse(t,e,n){switch(e){case"k":return mr(Gn,t);case"ko":return n.ordinalNumber(t,{unit:"hour"});default:return gr(e.length,t)}}validate(t,e){return e>=1&&e<=24}set(t,e,n){const r=n<=24?n%24:n;return t.setHours(r,0,0,0),t}},m:new class extends Wn{constructor(){super(...arguments),e(this,"priority",60),e(this,"incompatibleTokens",["t","T"])}parse(t,e,n){switch(e){case"m":return mr(Kn,t);case"mo":return n.ordinalNumber(t,{unit:"minute"});default:return gr(e.length,t)}}validate(t,e){return e>=0&&e<=59}set(t,e,n){return t.setMinutes(n,0,0),t}},s:new class extends Wn{constructor(){super(...arguments),e(this,"priority",50),e(this,"incompatibleTokens",["t","T"])}parse(t,e,n){switch(e){case"s":return mr(Vn,t);case"so":return n.ordinalNumber(t,{unit:"second"});default:return gr(e.length,t)}}validate(t,e){return e>=0&&e<=59}set(t,e,n){return t.setSeconds(n,0),t}},S:new class extends Wn{constructor(){super(...arguments),e(this,"priority",30),e(this,"incompatibleTokens",["t","T"])}parse(t,e){return fr(gr(e.length,t),(t=>Math.trunc(t*Math.pow(10,3-e.length))))}set(t,e,n){return t.setMilliseconds(n),t}},X:new class extends Wn{constructor(){super(...arguments),e(this,"priority",10),e(this,"incompatibleTokens",["t","T","x"])}parse(t,e){switch(e){case"X":return pr(ur,t);case"XX":return pr(cr,t);case"XXXX":return pr(lr,t);case"XXXXX":return pr(hr,t);default:return pr(dr,t)}}set(t,e,n){return e.timestampIsSet?t:ie(t,t.getTime()-Te(t)-n)}},x:new class extends Wn{constructor(){super(...arguments),e(this,"priority",10),e(this,"incompatibleTokens",["t","T","X"])}parse(t,e){switch(e){case"x":return pr(ur,t);case"xx":return pr(cr,t);case"xxxx":return pr(lr,t);case"xxxxx":return pr(hr,t);default:return pr(dr,t)}}set(t,e,n){return e.timestampIsSet?t:ie(t,t.getTime()-Te(t)-n)}},t:new class extends Wn{constructor(){super(...arguments),e(this,"priority",40),e(this,"incompatibleTokens","*")}parse(t){return wr(t)}set(t,e,n){return[ie(t,1e3*n),{timestampIsSet:!0}]}},T:new class extends Wn{constructor(){super(...arguments),e(this,"priority",20),e(this,"incompatibleTokens","*")}parse(t){return wr(t)}set(t,e,n){return[ie(t,n),{timestampIsSet:!0}]}}},Mr=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Dr=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Nr=/^'([^]*?)'?$/,Rr=/''/g,Pr=/\S/,Cr=/[a-zA-Z]/;function Ar(t,e,n,r){const o=Object.assign({},we()),a=r?.locale??o.locale??sn,s=r?.firstWeekContainsDate??r?.locale?.options?.firstWeekContainsDate??o.firstWeekContainsDate??o.locale?.options?.firstWeekContainsDate??1,i=r?.weekStartsOn??r?.locale?.options?.weekStartsOn??o.weekStartsOn??o.locale?.options?.weekStartsOn??0;if(""===e)return""===t?se(n):ie(n,NaN);const u={firstWeekContainsDate:s,weekStartsOn:i,locale:a},c=[new Yn],l=e.match(Dr).map((t=>{const e=t[0];if(e in On){return(0,On[e])(t,a.formatLong)}return t})).join("").match(Mr),d=[];for(let p of l){!r?.useAdditionalWeekYearTokens&&Rn(p)&&Pn(p,e,t),!r?.useAdditionalDayOfYearTokens&&Nn(p)&&Pn(p,e,t);const o=p[0],s=kr[o];if(s){const{incompatibleTokens:e}=s;if(Array.isArray(e)){const t=d.find((t=>e.includes(t.token)||t.token===o));if(t)throw new RangeError(`The format string mustn't contain \`${t.fullToken}\` and \`${p}\` at the same time`)}else if("*"===s.incompatibleTokens&&d.length>0)throw new RangeError(`The format string mustn't contain \`${p}\` and any other token at the same time`);d.push({token:o,fullToken:p});const r=s.run(t,p,a.match,u);if(!r)return ie(n,NaN);c.push(r.setter),t=r.rest}else{if(o.match(Cr))throw new RangeError("Format string contains an unescaped latin alphabet character `"+o+"`");if("''"===p?p="'":"'"===o&&(p=p.match(Nr)[1].replace(Rr,"'")),0!==t.indexOf(p))return ie(n,NaN);t=t.slice(p.length)}}if(t.length>0&&Pr.test(t))return ie(n,NaN);const h=c.map((t=>t.priority)).sort(((t,e)=>e-t)).filter(((t,e,n)=>n.indexOf(t)===e)).map((t=>c.filter((e=>e.priority===t)).sort(((t,e)=>e.subPriority-t.subPriority)))).map((t=>t[0]));let f=se(n);if(isNaN(f.getTime()))return ie(n,NaN);const m={};for(const p of h){if(!p.validate(f,u))return ie(n,NaN);const t=p.set(f,m,u);Array.isArray(t)?(f=t[0],Object.assign(m,t[1])):f=t}return ie(n,f)}function Fr(t){const e=se(t);return e.setMinutes(0,0,0),e}function jr(t){const e=se(t);return e.setMilliseconds(0),e}function Lr(t,e){const n=e?.additionalDigits??2,r=function(t){const e={},n=t.split(qr.dateTimeDelimiter);let r;if(n.length>2)return e;/:/.test(n[0])?r=n[0]:(e.date=n[0],r=n[1],qr.timeZoneDelimiter.test(e.date)&&(e.date=t.split(qr.timeZoneDelimiter)[0],r=t.substr(e.date.length,t.length)));if(r){const t=qr.timezone.exec(r);t?(e.time=r.replace(t[1],""),e.timezone=t[1]):e.time=r}return e}(t);let o;if(r.date){const t=function(t,e){const n=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+e)+"})|(\\d{2}|[+-]\\d{"+(2+e)+"})$)"),r=t.match(n);if(!r)return{year:NaN,restDateString:""};const o=r[1]?parseInt(r[1]):null,a=r[2]?parseInt(r[2]):null;return{year:null===a?o:100*a,restDateString:t.slice((r[1]||r[2]).length)}}(r.date,n);o=function(t,e){if(null===e)return new Date(NaN);const n=t.match(Hr);if(!n)return new Date(NaN);const r=!!n[4],o=Yr(n[1]),a=Yr(n[2])-1,s=Yr(n[3]),i=Yr(n[4]),u=Yr(n[5])-1;if(r)return function(t,e,n){return e>=1&&e<=53&&n>=0&&n<=6}(0,i,u)?function(t,e,n){const r=new Date(0);r.setUTCFullYear(t,0,4);const o=r.getUTCDay()||7,a=7*(e-1)+n+1-o;return r.setUTCDate(r.getUTCDate()+a),r}(e,i,u):new Date(NaN);{const t=new Date(0);return function(t,e,n){return e>=0&&e<=11&&n>=1&&n<=(_r[e]||(Ir(t)?29:28))}(e,a,s)&&function(t,e){return e>=1&&e<=(Ir(t)?366:365)}(e,o)?(t.setUTCFullYear(e,a,Math.max(o,s)),t):new Date(NaN)}}(t.restDateString,t.year)}if(!o||isNaN(o.getTime()))return new Date(NaN);const a=o.getTime();let s,i=0;if(r.time&&(i=function(t){const e=t.match(Br);if(!e)return NaN;const n=Wr(e[1]),r=Wr(e[2]),o=Wr(e[3]);if(!function(t,e,n){if(24===t)return 0===e&&0===n;return n>=0&&n<60&&e>=0&&e<60&&t>=0&&t<25}(n,r,o))return NaN;return n*fe+r*he+1e3*o}(r.time),isNaN(i)))return new Date(NaN);if(!r.timezone){const t=new Date(a+i),e=new Date(0);return e.setFullYear(t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()),e.setHours(t.getUTCHours(),t.getUTCMinutes(),t.getUTCSeconds(),t.getUTCMilliseconds()),e}return s=function(t){if("Z"===t)return 0;const e=t.match(Ur);if(!e)return 0;const n="+"===e[1]?-1:1,r=parseInt(e[2]),o=e[3]&&parseInt(e[3])||0;if(!function(t,e){return e>=0&&e<=59}(0,o))return NaN;return n*(r*fe+o*he)}(r.timezone),isNaN(s)?new Date(NaN):new Date(a+i+s)}const qr={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},Hr=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,Br=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,Ur=/^([+-])(\d{2})(?::?(\d{2}))?$/;function Yr(t){return t?parseInt(t):1}function Wr(t){return t&&parseFloat(t.replace(",","."))||0}const _r=[31,null,31,30,31,30,31,31,30,31,30,31];function Ir(t){return t%400==0||t%4==0&&t%100!=0}export{ge as A,xe as B,Fr as C,_e as D,jr as E,Qe as F,Ke as G,qe as H,$e as I,Le as J,Ge as K,Je as L,Ve as M,ae as N,Lr as a,Me as b,Ee as c,ce as d,ke as e,qn as f,ue as g,me as h,Ne as i,Se as j,Oe as k,le as l,We as m,Be as n,He as o,Ar as p,Ye as q,Re as r,Fe as s,se as t,je as u,Ue as v,Ae as w,Xe as x,Ie as y,ze as z};
