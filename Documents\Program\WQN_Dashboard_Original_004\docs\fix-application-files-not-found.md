# 🔧 Fix: "Application files not found" Error

## 🚨 **Your Exact Error**

```
16:00:36.459 GET http://**************:3001/ [HTTP/1.1 500 Internal Server Error 540ms]
error "Application files not found"
```

## 🔍 **Root Cause**

The server is running but the **production build files are missing**. The server can't find the `dist/index.html` file, which contains your React application.

## 🛠️ **IMMEDIATE FIX**

### **Option A: Automated Fix (Recommended)**

**SSH into your remote server and run:**

```bash
# SSH into the remote server
ssh user@**************

# Navigate to your application directory
cd /path/to/wqn-dashboard

# Download and run the fix script
chmod +x scripts/fix-remote-server.sh
./scripts/fix-remote-server.sh
```

### **Option B: Manual Fix**

**Step 1: SSH into Remote Server**
```bash
ssh user@**************
cd /path/to/wqn-dashboard
```

**Step 2: Install Dependencies**
```bash
npm ci --production
```

**Step 3: Build the Application**
```bash
# Clean previous build
rm -rf dist

# Build the application
npm run build

# Verify build
ls -la dist/
# Should show index.html and assets folder
```

**Step 4: Create Production Environment**
```bash
# Create .env.production if it doesn't exist
nano .env.production
```

**Add this content:**
```bash
NODE_ENV=production
PORT=3001
HOST=0.0.0.0

# Database Configuration - Using SQLite (same as development)
DATABASE_URL=sqlite:./src/server/database/water_quality.sqlite
DB_TYPE=sqlite
DB_PATH=./src/server/database/water_quality.sqlite

# Security
JWT_SECRET=your-super-secure-64-character-jwt-secret-change-this
CORS_ORIGIN=http://**************:3001,https://**************:3001
```

**Step 5: Use Existing SQLite Database**
```bash
# Check if database exists (keep existing data)
ls -la src/server/database/water_quality.sqlite

# If database doesn't exist, create it
if [ ! -f "src/server/database/water_quality.sqlite" ]; then
    npm run migrate
fi

# Ensure admin user exists (don't reset existing users)
npm run reset-admin-password
```

**Step 6: Start Application (No PM2)**
```bash
# Stop any existing processes
pkill -f "node.*3001" 2>/dev/null || true
pkill -f "tsx.*index.ts" 2>/dev/null || true

# Start the application in background
nohup npm run start:prod > server.log 2>&1 &

# Save the process ID
echo $! > server.pid

# Check if running
ps -p $(cat server.pid)
```

**Step 7: Configure Firewall**
```bash
# Ubuntu/Debian
sudo ufw allow 3001/tcp

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=3001/tcp
sudo firewall-cmd --reload
```

**Step 8: Test**
```bash
# Test locally on server
curl http://localhost:3001/health
curl http://localhost:3001/

# Check server status
ps -p $(cat server.pid)  # Check if process is running
tail -f server.log       # Check server logs
netstat -tuln | grep :3001  # Check if port is listening
```

## 🧪 **Verification**

After the fix, these should work:

### **✅ Health Check**
```bash
curl http://**************:3001/health
# Should return: {"status":"ok","timestamp":"..."}
```

### **✅ Main Application**
```bash
curl http://**************:3001/
# Should return HTML with "Ecocoast Water Quality Monitor"
```

### **✅ Browser Access**
- Open: `http://**************:3001`
- Should show the login page
- Login with: `admin` / `admin@123`

## 🔍 **Troubleshooting**

### **If Build Fails:**
```bash
# Try alternative build command
npm run build:prod

# Or full production build
npm run build:prod:full

# Check for TypeScript errors
npm run type-check
```

### **If Still Getting 500 Error:**
```bash
# Check server logs
tail -f server.log

# Check if dist directory exists
ls -la dist/

# Check if index.html exists
ls -la dist/index.html

# Restart the application
kill $(cat server.pid)
nohup npm run start:prod > server.log 2>&1 &
echo $! > server.pid
```

### **If Port Issues:**
```bash
# Check what's using port 3001
netstat -tulpn | grep :3001

# Kill any conflicting processes
sudo kill -9 $(lsof -t -i:3001)

# Restart application
kill $(cat server.pid) 2>/dev/null || true
nohup npm run start:prod > server.log 2>&1 &
echo $! > server.pid
```

## 📋 **Build Verification Checklist**

After running the build, verify these files exist:

```bash
# Required files
✅ dist/index.html          # Main HTML file
✅ dist/assets/             # Assets directory
✅ dist/assets/js/          # JavaScript files
✅ dist/assets/css/         # CSS files
✅ dist/assets/images/      # Image files

# Check file sizes (should not be 0 bytes)
du -h dist/index.html       # Should be > 1KB
du -h dist/assets/          # Should be > 100KB
```

## 🎯 **Expected File Structure**

Your `dist` directory should look like this:

```
dist/
├── index.html              # Main HTML file (~2-5KB)
├── favicon.png             # Favicon
└── assets/
    ├── css/
    │   └── index-[hash].css    # Main CSS (~50-200KB)
    ├── js/
    │   ├── index-[hash].js     # Main JS bundle (~500KB-2MB)
    │   ├── vendor-[hash].js    # Vendor libraries (~1-3MB)
    │   └── [other-chunks].js   # Other JS chunks
    └── images/
        └── logo-[hash].png     # Logo and other images
```

## 🚀 **Quick Commands Summary**

```bash
# Complete fix in one go:
npm ci --production && \
npm run build && \
npm run migrate && \
npm run reset-admin-password && \
nohup npm run start:prod > server.log 2>&1 & \
echo $! > server.pid

# Test after fix:
curl http://localhost:3001/health
curl http://**************:3001/health

# Check status:
ps -p $(cat server.pid)
tail -f server.log
```

## 🎉 **Success Indicators**

After the fix, you should see:

1. **✅ No more "Application files not found" error**
2. **✅ Browser loads the login page**
3. **✅ Can login with admin/admin@123**
4. **✅ Dashboard displays correctly**
5. **✅ Server process is running**: `ps -p $(cat server.pid)`

## 📞 **If Still Not Working**

1. **Check server logs**: `tail -f server.log`
2. **Verify build output**: `ls -la dist/`
3. **Test locally first**: `curl http://localhost:3001/`
4. **Check firewall**: `sudo ufw status`
5. **Verify environment**: `cat .env.production`

---

**🎯 The "Application files not found" error will be completely resolved after building the production files and deploying them correctly!**
