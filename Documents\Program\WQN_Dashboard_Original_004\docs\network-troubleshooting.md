# 🌐 Network Access Troubleshooting Guide

## 🚨 **Current Issue Analysis**

Your server shows:
- ✅ **Local Access (127.0.0.1:3001)**: Working
- ❌ **Network Access (0.0.0.0:3001)**: Not working

## 🔍 **Root Cause Analysis**

Based on the audit, the main issues are:

1. **Host Binding Configuration**: Server may not be binding to all network interfaces
2. **Windows Firewall**: Blocking incoming connections on port 3001
3. **CORS Configuration**: Conflicting CORS settings
4. **Environment Configuration**: Mismatch between development and production settings

## 🛠️ **Step-by-Step Fix**

### **Step 1: Update Environment Configuration**

Your `.env` file has been updated with:
```bash
HOST=0.0.0.0  # Bind to all network interfaces
CORS_ORIGIN=http://localhost:5173,http://127.0.0.1:5173,http://*************:5173,http://*************:3001
```

### **Step 2: Configure Windows Firewall**

Run the firewall setup script as Administrator:
```bash
# Right-click and "Run as administrator"
scripts\setup-firewall.bat
```

Or manually add firewall rules:
```bash
# Open Command Prompt as Administrator
netsh advfirewall firewall add rule name="WQN Dashboard Server" dir=in action=allow protocol=TCP localport=3001
netsh advfirewall firewall add rule name="WQN Dashboard Frontend" dir=in action=allow protocol=TCP localport=5173
```

### **Step 3: Test Network Connectivity**

Use the new network test script:
```bash
npm run network-test
```

### **Step 4: Restart Server**

Stop and restart your server:
```bash
# Stop current server (Ctrl+C)
# Then restart
npm run dev
```

## 🧪 **Testing Network Access**

### **From Same Computer:**
```bash
# Test localhost
curl http://127.0.0.1:3001/health
curl http://localhost:3001/health

# Test network IP
curl http://*************:3001/health
```

### **From Another Device:**
```bash
# Replace with your actual IP
curl http://*************:3001/health

# Or open in browser
http://*************:3001
```

## 🔧 **Advanced Troubleshooting**

### **Check Server Binding**
```bash
netstat -an | findstr :3001
```

Should show:
```
TCP    0.0.0.0:3001           0.0.0.0:0              LISTENING
```

### **Check Firewall Status**
```bash
netsh advfirewall show allprofiles
```

### **Test Port Accessibility**
```bash
# From another computer
telnet ************* 3001
```

## 🚀 **Quick Fix Commands**

```bash
# 1. Update environment
echo HOST=0.0.0.0 >> .env

# 2. Add firewall rule (as Administrator)
netsh advfirewall firewall add rule name="WQN Dashboard" dir=in action=allow protocol=TCP localport=3001

# 3. Test connectivity
npm run network-test

# 4. Restart server
npm run dev
```

## 📱 **Mobile/Device Access**

Once fixed, access from other devices:
- **Frontend**: `http://*************:5173`
- **API**: `http://*************:3001/api`
- **Health Check**: `http://*************:3001/health`

## ⚠️ **Common Issues**

### **Issue 1: Still Can't Access**
- Check router firewall settings
- Ensure devices are on same network
- Try disabling Windows Firewall temporarily for testing

### **Issue 2: CORS Errors**
- Verify CORS_ORIGIN includes your network IP
- Check browser console for specific CORS errors

### **Issue 3: Server Not Starting**
- Check if port 3001 is already in use
- Verify .env file syntax
- Check server logs for errors

## 🔒 **Security Considerations**

- Only allow network access on trusted networks
- Consider using HTTPS in production
- Regularly update firewall rules
- Monitor access logs

## 📞 **Support**

If issues persist:
1. Run `npm run network-test` and share output
2. Check Windows Event Viewer for firewall blocks
3. Verify network topology (router, switches, etc.)
4. Test with different devices/browsers
