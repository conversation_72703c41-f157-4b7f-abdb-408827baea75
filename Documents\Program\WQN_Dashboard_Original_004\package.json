{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "node scripts/port-manager.js", "dev:start": "concurrently -k \"npm run server\" \"npm run client\"", "start": "node scripts/port-manager.js", "client": "vite --host", "server": "tsx watch src/server/index.ts", "network-test": "node scripts/network-test.js", "network-test-full": "node scripts/comprehensive-network-test.js", "build": "npx tsc && vite build", "build:prod": "node scripts/build-simple.js", "build:prod:full": "node scripts/build-production.js", "build:dev": "vite build --mode development", "build:staging": "vite build --mode staging", "start:prod": "node scripts/start-production.js", "preview:prod": "vite preview --mode production --host", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview --host", "clean": "rimraf dist node_modules src/server/database/water_quality.sqlite", "clean:build": "<PERSON><PERSON><PERSON> dist", "migrate": "tsx src/server/migrations/migrate.ts", "type-check": "tsc --noEmit", "analyze": "npx vite-bundle-analyzer dist", "env:switch": "node scripts/env-switcher.js", "env:status": "node scripts/env-switcher.js status", "env:prod": "node scripts/env-switcher.js switch production", "env:dev": "node scripts/env-switcher.js switch development", "audit:security": "npm audit --audit-level moderate", "audit:deps": "npm outdated", "audit:bundle": "npm run build && npm run analyze", "cleanup:format": "prettier --write src/**/*.{ts,tsx,js,jsx}", "quality:check": "npm run lint && npm run type-check", "maintenance:weekly": "npm run audit:security && npm run audit:deps && npm run quality:check", "maintenance:full": "npm run cleanup:format && npm run quality:check && npm run audit:bundle", "maintenance": "node scripts/maintenance.js", "maintenance:quick": "node scripts/maintenance.js --quick", "reset-admin-password": "node scripts/reset-admin-password.js", "test-admin-login": "node scripts/reset-admin-password.js --test"}, "dependencies": {"@headlessui/react": "^2.0.0", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.8.0", "axios": "^1.6.7", "bcrypt": "^5.1.1", "chart.js": "^4.4.1", "chartjs-adapter-date-fns": "^3.0.0", "compression": "^1.8.0", "cors": "^2.8.5", "date-fns": "^3.3.1", "dotenv": "^16.4.5", "express": "^4.18.3", "express-rate-limit": "^7.5.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.344.0", "maplibre-gl": "^4.1.1", "node-fetch": "^3.3.2", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.3", "sequelize": "^6.37.1", "sqlite3": "^5.1.7", "xss": "^1.0.14", "zod": "^3.22.4"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-rate-limit": "^5.1.3", "@types/helmet": "^4.0.0", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.11.24", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.18", "concurrently": "^8.2.2", "postcss": "^8.4.35", "rimraf": "^5.0.5", "tailwindcss": "^3.4.1", "terser": "^5.40.0", "tsx": "^4.7.1", "typescript": "^5.3.3", "vite": "^5.1.4"}}