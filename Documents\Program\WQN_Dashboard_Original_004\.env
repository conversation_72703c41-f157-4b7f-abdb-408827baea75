# Water Quality Network Dashboard - Development Environment
# WARNING: This file contains development settings. Use .env.example for production setup.

# Database Configuration
DATABASE_URL=sqlite:./src/server/database/water_quality.sqlite

# Security Configuration (DEVELOPMENT ONLY - CHANGE FOR PRODUCTION)
JWT_SECRET=dev-jwt-secret-change-this-for-production-use-64-chars-minimum
JWT_EXPIRES_IN=24h
JWT_ISSUER=wqn-dashboard
JWT_AUDIENCE=wqn-users
SESSION_SECRET=dev-session-secret-change-this-for-production-use-64-chars-minimum
BCRYPT_ROUNDS=12

# External API Credentials
VITE_API_KEY=ed1c989efe294501a8ccadd00097c786
VITE_API_SECRET=bf5d16de48ed477081ea58e517078d25
VITE_ACCOUNT=Ecocoast

# API URLs
VITE_TOKEN_URL=https://vip.boqucloud.com/api/token/get
VITE_MONITOR_OPEN_URL=https://vip.boqucloud.com/api/eg/monitor/open
VITE_SENSOR_DATA_URL=https://vip.boqucloud.com/api/eg/signal/value

# Server Configuration
PORT=3001
HOST=0.0.0.0
NODE_ENV=development
CORS_ORIGIN=http://localhost:5173,http://127.0.0.1:5173,http://*************:5173,http://*************:3001

# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_NOTIFICATIONS=false
ENABLE_EXPORT=true
VITE_DEV_MODE=true
