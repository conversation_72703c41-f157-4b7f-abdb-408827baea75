import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // Load env file based on `mode` in the current working directory.
  // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
  const env = loadEnv(mode, process.cwd(), '');

  const isProduction = mode === 'production';
  const isDevelopment = mode === 'development';

  return {
    plugins: [react()],

    // Base URL configuration
    base: '/',

    // Development server configuration
    server: {
      host: true, // Allow external access
      port: 3000,
      proxy: {
        '/api': {
          target: env.VITE_API_TARGET || 'http://127.0.0.1:3001',
          changeOrigin: true,
          secure: false,
          rewrite: (path) => path.replace(/^\/api/, '/api'),
          configure: (proxy, _options) => {
            if (isDevelopment) {
              proxy.on('error', (err, _req, _res) => {
                console.log('proxy error', err);
              });
              proxy.on('proxyReq', (proxyReq, req, _res) => {
                console.log('Sending Request to the Target:', req.method, req.url);
              });
              proxy.on('proxyRes', (proxyRes, req, _res) => {
                console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
              });
            }
          },
        },
      },
    },

    // Dependency optimization
    optimizeDeps: {
      exclude: [
        'bcrypt',
        'sqlite3',
        '@mapbox/node-pre-gyp'
      ]
    },

    // Build configuration
    build: {
      // Output directory
      outDir: 'dist',

      // Generate sourcemaps for production debugging (optional)
      sourcemap: isProduction ? false : true,

      // Minification
      minify: isProduction ? 'terser' : false,

      // Terser options for production
      ...(isProduction && {
        terserOptions: {
          compress: {
            drop_console: false, // Temporarily enable console for debugging
            drop_debugger: true,
            pure_funcs: []
          },
          mangle: {
            safari10: true
          },
          format: {
            comments: false
          }
        }
      }),

      // Rollup options
      rollupOptions: {
        external: [
          'bcrypt',
          'sqlite3',
          '@mapbox/node-pre-gyp'
        ],

        // Code splitting for better caching
        output: {
          manualChunks: isProduction ? {
            // Vendor chunk for React and core libraries
            vendor: ['react', 'react-dom', 'react-router-dom'],

            // Charts chunk for chart.js and related
            charts: ['chart.js', 'react-chartjs-2', 'chartjs-adapter-date-fns'],

            // UI chunk for UI components
            ui: ['lucide-react', '@headlessui/react'],

            // Utils chunk for utility libraries
            utils: ['axios', 'date-fns', 'zod']
          } : undefined,

          // Asset file naming
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split('.');
            const ext = info[info.length - 1];
            if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
              return `assets/images/[name]-[hash][extname]`;
            }
            if (/css/i.test(ext)) {
              return `assets/css/[name]-[hash][extname]`;
            }
            return `assets/[name]-[hash][extname]`;
          },

          // Chunk file naming
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js'
        }
      },

      // CommonJS options
      commonjsOptions: {
        transformMixedEsModules: true
      },

      // Target modern browsers in production
      target: isProduction ? 'es2020' : 'esnext',

      // Chunk size warning limit
      chunkSizeWarningLimit: 1000
    },

    // Path resolution
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src')
      }
    },

    // Environment variables
    define: {
      __APP_VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
      __IS_PRODUCTION__: JSON.stringify(isProduction)
    },

    // Preview server configuration (for production preview)
    preview: {
      port: 4173,
      host: true
    }
  };
});
