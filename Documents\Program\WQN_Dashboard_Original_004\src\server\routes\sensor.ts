import express from 'express';
import cors from 'cors';
import { authMiddleware } from '../middleware/auth';
import fetch from 'node-fetch';
import dotenv from 'dotenv';

dotenv.config(); // Load environment variables from .env file

const router = express.Router();

// CORS is handled globally in the main server configuration
// Removing duplicate CORS configuration to prevent conflicts

/**
 * Fetches an access token from the external API.
 * 
 * @returns {Promise<string>} A promise that resolves to the access token string.
 */
async function getToken(): Promise<string> {
  const tokenUrl = process.env.TOKEN_URL!; // URL to fetch the token from environment variable
  const response = await fetch(tokenUrl, {
    method: 'POST', // Use POST method to request the token
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded' // Set content type for the request
    },
    body: new URLSearchParams({
      grant_type: "client_credentials", // Grant type for the token request
      appKey: process.env.APP_KEY!, // Application key from environment variable
      appSecret: process.env.APP_SECRET!, // Application secret from environment variable
      account: process.env.ACCOUNT! // Account identifier from environment variable
    })
  });

  const data = await response.json(); // Parse the JSON response
  return data.data.access_token; // Return the access token
}

export default router; 
