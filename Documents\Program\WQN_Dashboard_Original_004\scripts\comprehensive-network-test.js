#!/usr/bin/env node

/**
 * Comprehensive Network Test Script
 * Tests all aspects of network connectivity including CORS, CSP, and protocol issues
 */

import fetch from 'node-fetch';
import { exec } from 'child_process';
import { promisify } from 'util';
import { networkInterfaces } from 'os';

const execAsync = promisify(exec);

const COLORS = {
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  RESET: '\x1b[0m',
  BOLD: '\x1b[1m'
};

function log(message, color = COLORS.WHITE) {
  console.log(`${color}${message}${COLORS.RESET}`);
}

/**
 * Get all network interfaces
 */
function getNetworkIPs() {
  const interfaces = networkInterfaces();
  const ips = [];
  
  for (const interfaceName in interfaces) {
    const iface = interfaces[interfaceName];
    for (const config of iface) {
      if (config.family === 'IPv4' && !config.internal) {
        ips.push({
          interface: interfaceName,
          ip: config.address,
          netmask: config.netmask
        });
      }
    }
  }
  
  return ips;
}

/**
 * Test HTTP vs HTTPS protocol handling
 */
async function testProtocolHandling(ip, port) {
  const tests = [
    { protocol: 'http', url: `http://${ip}:${port}/health` },
    { protocol: 'https', url: `https://${ip}:${port}/health` }
  ];
  
  for (const test of tests) {
    try {
      const response = await fetch(test.url, { 
        timeout: 5000,
        headers: { 'User-Agent': 'WQN-Protocol-Test/1.0' }
      });
      
      if (response.ok) {
        log(`✅ ${test.protocol.toUpperCase()}: ${test.url} - Working`, COLORS.GREEN);
      } else {
        log(`❌ ${test.protocol.toUpperCase()}: ${test.url} - HTTP ${response.status}`, COLORS.RED);
      }
    } catch (error) {
      if (test.protocol === 'https' && error.message.includes('ECONNREFUSED')) {
        log(`ℹ️  HTTPS: ${test.url} - Expected (no HTTPS configured)`, COLORS.BLUE);
      } else {
        log(`❌ ${test.protocol.toUpperCase()}: ${test.url} - ${error.message}`, COLORS.RED);
      }
    }
  }
}

/**
 * Test CORS configuration
 */
async function testCORS(ip, port) {
  const testOrigins = [
    `http://${ip}:3000`,
    `http://${ip}:${port}`,
    `http://localhost:3000`,
    `http://127.0.0.1:3000`
  ];
  
  for (const origin of testOrigins) {
    try {
      const response = await fetch(`http://${ip}:${port}/health`, {
        headers: {
          'Origin': origin,
          'User-Agent': 'WQN-CORS-Test/1.0'
        }
      });
      
      const corsHeader = response.headers.get('access-control-allow-origin');
      if (corsHeader === origin || corsHeader === '*') {
        log(`✅ CORS: Origin ${origin} - Allowed`, COLORS.GREEN);
      } else {
        log(`❌ CORS: Origin ${origin} - Blocked (${corsHeader})`, COLORS.RED);
      }
    } catch (error) {
      log(`❌ CORS: Origin ${origin} - ${error.message}`, COLORS.RED);
    }
  }
}

/**
 * Test CSP headers
 */
async function testCSP(ip, port) {
  try {
    const response = await fetch(`http://${ip}:${port}`, {
      headers: { 'User-Agent': 'WQN-CSP-Test/1.0' }
    });
    
    const cspHeader = response.headers.get('content-security-policy');
    if (cspHeader) {
      log(`ℹ️  CSP Header found: ${cspHeader.substring(0, 100)}...`, COLORS.BLUE);
      
      // Check if current IP is allowed
      if (cspHeader.includes(`http://${ip}:${port}`) || cspHeader.includes("'self'")) {
        log(`✅ CSP: Current IP ${ip} is allowed`, COLORS.GREEN);
      } else {
        log(`❌ CSP: Current IP ${ip} may be blocked`, COLORS.RED);
      }
      
      // Check for upgrade-insecure-requests
      if (cspHeader.includes('upgrade-insecure-requests')) {
        log(`⚠️  CSP: Contains upgrade-insecure-requests (may cause HTTPS issues)`, COLORS.YELLOW);
      } else {
        log(`✅ CSP: No forced HTTPS upgrades`, COLORS.GREEN);
      }
    } else {
      log(`ℹ️  CSP: No CSP header found`, COLORS.BLUE);
    }
  } catch (error) {
    log(`❌ CSP Test: ${error.message}`, COLORS.RED);
  }
}

/**
 * Test main application loading
 */
async function testMainApplication(ip, port) {
  try {
    const response = await fetch(`http://${ip}:${port}`, {
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'User-Agent': 'WQN-App-Test/1.0'
      }
    });
    
    if (response.ok) {
      const html = await response.text();
      
      if (html.includes('<div id="root">') && html.includes('Ecocoast Water Quality Monitor')) {
        log(`✅ Main App: ${ip}:${port} - React app loading correctly`, COLORS.GREEN);
        
        // Check for static assets
        const jsMatch = html.match(/src="([^"]*\.js)"/);
        const cssMatch = html.match(/href="([^"]*\.css)"/);
        
        if (jsMatch) {
          const jsUrl = `http://${ip}:${port}${jsMatch[1]}`;
          try {
            const jsResponse = await fetch(jsUrl);
            if (jsResponse.ok) {
              log(`✅ Static Assets: JavaScript loading correctly`, COLORS.GREEN);
            } else {
              log(`❌ Static Assets: JavaScript failed (${jsResponse.status})`, COLORS.RED);
            }
          } catch (error) {
            log(`❌ Static Assets: JavaScript error - ${error.message}`, COLORS.RED);
          }
        }
        
        return true;
      } else {
        log(`❌ Main App: ${ip}:${port} - HTML served but not React app`, COLORS.RED);
        return false;
      }
    } else {
      log(`❌ Main App: ${ip}:${port} - HTTP ${response.status}`, COLORS.RED);
      return false;
    }
  } catch (error) {
    log(`❌ Main App: ${ip}:${port} - ${error.message}`, COLORS.RED);
    return false;
  }
}

/**
 * Test firewall and port accessibility
 */
async function testFirewall(port) {
  try {
    const { stdout } = await execAsync(`netstat -an | findstr :${port}`);
    
    if (stdout.includes('LISTENING')) {
      log(`✅ Firewall: Port ${port} is listening`, COLORS.GREEN);
      
      // Check binding
      if (stdout.includes('0.0.0.0')) {
        log(`✅ Firewall: Bound to all interfaces (0.0.0.0)`, COLORS.GREEN);
      } else if (stdout.includes('127.0.0.1')) {
        log(`⚠️  Firewall: Bound to localhost only (127.0.0.1)`, COLORS.YELLOW);
      }
    } else {
      log(`❌ Firewall: Port ${port} not listening`, COLORS.RED);
    }
  } catch (error) {
    log(`❌ Firewall Test: ${error.message}`, COLORS.RED);
  }
}

/**
 * Main comprehensive test
 */
async function runComprehensiveTest() {
  const PORT = process.env.PORT || 3001;
  
  log(`${COLORS.BOLD}${COLORS.CYAN}🔍 Comprehensive Network Connectivity Test${COLORS.RESET}`);
  log(`${COLORS.BOLD}${COLORS.CYAN}============================================${COLORS.RESET}\n`);
  
  // Get network information
  log(`${COLORS.BOLD}🌐 Network Information:${COLORS.RESET}`);
  const networkIPs = getNetworkIPs();
  for (const iface of networkIPs) {
    log(`   ${iface.interface}: ${iface.ip} (${iface.netmask})`, COLORS.BLUE);
  }
  
  // Test firewall and port binding
  log(`\n${COLORS.BOLD}🔥 Firewall & Port Test:${COLORS.RESET}`);
  await testFirewall(PORT);
  
  // Test each network interface
  for (const iface of networkIPs) {
    const ip = iface.ip;
    
    log(`\n${COLORS.BOLD}🧪 Testing Interface: ${iface.interface} (${ip})${COLORS.RESET}`);
    
    // Test protocol handling
    log(`\n📡 Protocol Tests:`);
    await testProtocolHandling(ip, PORT);
    
    // Test CORS
    log(`\n🌐 CORS Tests:`);
    await testCORS(ip, PORT);
    
    // Test CSP
    log(`\n🛡️  CSP Tests:`);
    await testCSP(ip, PORT);
    
    // Test main application
    log(`\n🚀 Main Application Test:`);
    await testMainApplication(ip, PORT);
  }
  
  // Test localhost specifically
  log(`\n${COLORS.BOLD}🏠 Localhost Tests:${COLORS.RESET}`);
  await testProtocolHandling('127.0.0.1', PORT);
  await testMainApplication('127.0.0.1', PORT);
  
  // Final recommendations
  log(`\n${COLORS.BOLD}💡 Recommendations:${COLORS.RESET}`);
  log(`1. Use HTTP (not HTTPS) for local network access: http://192.168.x.x:${PORT}`, COLORS.CYAN);
  log(`2. Ensure firewall allows inbound connections on port ${PORT}`, COLORS.CYAN);
  log(`3. Check that CORS_ORIGIN includes your network IPs`, COLORS.CYAN);
  log(`4. Verify CSP headers allow network access`, COLORS.CYAN);
  log(`5. Test from different devices on the same network`, COLORS.CYAN);
}

// Run the comprehensive test
runComprehensiveTest().catch(error => {
  log(`❌ Test failed: ${error.message}`, COLORS.RED);
  process.exit(1);
});
