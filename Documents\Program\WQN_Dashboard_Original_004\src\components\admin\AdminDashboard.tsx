// Import necessary libraries and components
import { Routes, Route, useNavigate } from 'react-router-dom';
import { LogOut } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import Sidebar from './Sidebar';
import DashboardOverview from './DashboardOverview';
import SensorManagement from './SensorManagement';
import UserManagement from './UserManagement';
import AccessControl from './AccessControl';
import Settings from './Settings';
import panelBgImage from '../../assets/images/panel_bg.jpg';

// AdminDashboard component that serves as the main interface for admin functionalities
function AdminDashboard() {
  // Hook to programmatically navigate
  const navigate = useNavigate();
  // Destructure logout function from authentication context
  const { logout } = useAuth();

  // Function to handle user logout
  const handleLogout = () => {
    logout(); // Call logout function
    navigate('/login'); // Redirect to login page after logout
  };

  return (
    // Main container with background image and styling
    <div className="flex h-screen bg-cover bg-center" style={{ backgroundImage: `url(${panelBgImage})` }}>
      <div className="flex h-screen w-full bg-black/40 backdrop-blur-[2px]">
        <Sidebar /> {/* Sidebar component for navigation */}
        <div className="flex-1 overflow-auto bg-gray-100/80 backdrop-blur-sm">
          {/* Header section with title and logout button */}
          <div className="sticky top-0 z-10 bg-gray-900/90 backdrop-blur-sm text-white px-6 py-4 flex justify-between items-center">
            <h1 className="text-xl font-semibold">Admin Dashboard</h1>
            <button
              onClick={handleLogout} // Logout button that triggers handleLogout
              className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
            >
              <LogOut className="w-4 h-4" /> {/* Logout icon */}
              <span>Logout</span> {/* Logout text */}
            </button>
          </div>
          {/* Define routes for different admin functionalities */}
          <Routes>
            <Route path="/" element={<DashboardOverview />} /> {/* Default route for dashboard overview */}
            <Route path="/sensors" element={<SensorManagement />} /> {/* Route for sensor management */}
            <Route path="/users" element={<UserManagement />} /> {/* Route for user management */}
            <Route path="/access" element={<AccessControl />} /> {/* Route for access control */}
            <Route path="/settings" element={<Settings />} /> {/* Route for settings */}
          </Routes>
        </div>
      </div>
    </div>
  );
}

// Export the AdminDashboard component for use in other parts of the application
export default AdminDashboard;
