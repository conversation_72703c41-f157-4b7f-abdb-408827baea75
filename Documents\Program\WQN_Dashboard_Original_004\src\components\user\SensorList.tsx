import React, { useEffect, useState } from 'react';
import { Bell, MapPin, Signal, RefreshCw, Map } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { getSensorSignalData, SignalReading } from '../../utils/sensorDataUtils';
import { api } from '../../api/client';
import { Permission, Sensor } from '../../types';
import { Header } from '../shared/Header';
import MapPopup from './MapPopup';
import panelBgImage from '../../assets/images/panel_bg.jpg';

// Interface defining the props for the SensorCard component
interface SensorCardProps {
  name: string; // Name of the sensor
  id: string; // Unique identifier for the sensor
  apiId: string; // API identifier for the sensor
  onViewDetails: () => void; // Callback function to view sensor details
}

// SensorCard component for displaying individual sensor information
const SensorCard: React.FC<SensorCardProps> = ({
  name,
  id,
  apiId,
  onViewDetails
}) => {
  const [signalData, setSignalData] = useState<SignalReading | null>(null); // State to hold signal data
  const [isLoading, setIsLoading] = useState(true); // State to manage loading status
  const [isPreloading, setIsPreloading] = useState(false); // State for preloading indicator

  // Effect to load signal data when the component mounts or id changes
  useEffect(() => {
    const loadSignalData = async () => {
      const data = await getSensorSignalData(id); // Fetch signal data for the sensor
      setSignalData(data); // Update state with fetched data
      setIsLoading(false); // Set loading to false
    };

    loadSignalData(); // Call the function to load data
  }, [id]);

  // Enhanced preloading on hover
  const handleMouseEnter = () => {
    if (window.optimizedApiService && !isPreloading) {
      setIsPreloading(true);
      console.log(`🔄 Preloading data for sensor: ${id}`);

      window.optimizedApiService.preloadSensorData({
        equipmentId: id,
        apiId,
        name: name,
        location: '',
        status: 'active'
      }).then(() => {
        console.log(`✅ Preloaded data for sensor: ${id}`);
      }).catch(err => {
        console.warn('Failed to preload sensor data:', err);
      }).finally(() => {
        setIsPreloading(false);
      });
    }
  };

  // Render a loading skeleton if data is still being fetched
  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 space-y-4">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        </div>
      </div>
    );
  }

  // If no signal data is available, return null
  if (!signalData) return null;

  // Function to determine the color of the signal strength indicator based on level
  const getSignalStrengthColor = (level: number) => {
    if (level >= 80) return 'text-green-500'; // Strong signal
    if (level >= 60) return 'text-yellow-500'; // Moderate signal
    if (level >= 40) return 'text-orange-500'; // Weak signal
    return 'text-red-500'; // No signal
  };

  // Function to handle viewing sensor details with preloading
  const handleViewDetails = () => {
    console.log('Navigating to sensor details:', id); // Log the action

    // Preload sensor data for faster navigation
    if (window.optimizedApiService) {
      window.optimizedApiService.preloadSensorData({ equipmentId: id, apiId, name: name, location: '', status: 'active' }).catch(err => {
        console.warn('Failed to preload sensor data:', err);
      });
    }

    onViewDetails(); // Call the provided callback to view details
  };

  // Render the sensor card with its details
  return (
    <div
      className="bg-white rounded-lg shadow-md p-6 space-y-4 hover:shadow-lg transition-shadow duration-200 relative"
      onMouseEnter={handleMouseEnter}
    >
      {isPreloading && (
        <div className="absolute top-2 right-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        </div>
      )}

      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{name}</h3>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <span>ID: {id}</span>
            {/* <span></span> */}
            {/* <span>API ID: {apiId}</span> */}
          </div>
        </div>
        <Bell className="w-5 h-5 text-gray-400" />
      </div>

      <div className="flex items-center space-x-2">
        <div className={`w-2 h-2 rounded-full ${
          signalData.status === 'online' ? 'bg-green-500' : 'bg-red-500'
        }`} />
        <span className="text-sm font-medium text-gray-700 capitalize">{signalData.status}</span>
        <div className="flex-1 flex justify-end">
          <div className="flex items-center">
            <span className="text-sm text-gray-600 mr-2">{signalData.signalLevel}%</span>
            <Signal className={`w-4 h-4 ${getSignalStrengthColor(signalData.signalLevel)}`} />
          </div>
        </div>
      </div>

      <div className="flex items-start space-x-2">
        <MapPin className="w-4 h-4 text-gray-400 mt-0.5" />
        <span className="text-sm text-gray-600">
          Initial Location: {signalData.latitude}, {signalData.longitude}
        </span>
      </div>

      <button
        onClick={handleViewDetails} // Handle click to view details
        className="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-gray-50 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
      >
        View Details
      </button>
    </div>
  );
};

// SensorList component for displaying a list of sensors
const SensorList: React.FC = () => {
  const navigate = useNavigate(); // Hook to programmatically navigate
  const { logout, getCurrentUser } = useAuth(); // Auth context for user management
  const [isLoading, setIsLoading] = useState(true); // State to manage loading status
  const [isRefreshing, setIsRefreshing] = useState(false); // State to manage refresh status
  const [error, setError] = useState<string>(''); // State to hold error messages
  const [availableSensors, setAvailableSensors] = useState<Sensor[]>([]); // State to hold available sensors
  const [userFullName, setUserFullName] = useState<string>(''); // State to hold user's full name
  const [showMap, setShowMap] = useState(false);
  const [sensorSignalData, setSensorSignalData] = useState<Record<string, SignalReading>>({});

  // Function to fetch the user's sensors from the API
  const fetchUserSensors = async () => {
    try {
      setIsRefreshing(true); // Set refreshing state to true
      setError(''); // Clear any previous errors
      const currentUser = getCurrentUser(); // Get the current user

      if (!currentUser) {
        navigate('/login'); // Redirect to login if no user is found
        return;
      }

      const sensorsResponse = await api.getUserSensors(); // Fetch sensors from API
      setAvailableSensors(sensorsResponse.data); // Update state with fetched sensors
    } catch (error: any) {
      console.error('Failed to fetch sensors:', error); // Log error
      setError(error.response?.data?.error || 'Failed to load sensors. Please try again.'); // Set error message
    } finally {
      setIsRefreshing(false); // Reset refreshing state
      setIsLoading(false); // Reset loading state
    }
  };

  // Initial load of sensors when the component mounts
  useEffect(() => {
    fetchUserSensors(); // Call function to fetch sensors
  }, []);

  // Effect to fetch the user's full name
  useEffect(() => {
    const fetchUserFullName = async () => {
      try {
        const currentUser = getCurrentUser(); // Get the current user
        if (!currentUser) {
          navigate('/login'); // Redirect to login if no user is found
          return;
        }

        const response = await api.getUserProfile(); // Fetch user profile from API
        if (response.data) {
          setUserFullName(response.data.fullName); // Set full name if available
        } else {
          setUserFullName(currentUser.username); // Fallback to username
        }
      } catch (error) {
        console.error('Failed to fetch user details:', error); // Log error
        const currentUser = getCurrentUser();
        setUserFullName(currentUser?.username || 'User'); // Set fallback name
      }
    };

    fetchUserFullName(); // Call function to fetch user full name
  }, [getCurrentUser, navigate]);

  // Function to handle user logout
  const handleLogout = () => {
    logout(); // Call logout function
    navigate('/login'); // Redirect to login page
  };

  // Effect to fetch signal data for all sensors
  useEffect(() => {
    const fetchAllSensorSignalData = async () => {
      const signalData: Record<string, SignalReading> = {};
      for (const sensor of availableSensors) {
        const data = await getSensorSignalData(sensor.equipmentId);
        if (data) {
          signalData[sensor.equipmentId] = data;
        }
      }
      setSensorSignalData(signalData);
    };

    if (availableSensors.length > 0) {
      fetchAllSensorSignalData();
    }
  }, [availableSensors]);

  // Render loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading sensors...</p>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-xl text-red-600">{error}</p>
          <button
            onClick={() => window.location.reload()} // Reload the page on button click
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Render the main sensor list
  return (
    <div className="min-h-screen bg-gray-50 bg-cover bg-center bg-no-repeat bg-blend-overlay bg-black/65 backdrop-blur-sm" style={{ backgroundImage: `url(${panelBgImage})` }}>
      <Header
        userFullName={userFullName} // Pass user's full name to Header
        onLogout={handleLogout} // Pass logout handler to Header
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-black">Sensor List</h2>
            {/* <p className="text-gray-600">
              Showing {availableSensors.length} authorized sensors
            </p> */}
          </div>
          <div className="flex space-x-2">
            <button
              onClick={fetchUserSensors} // Refresh sensors on button click
              disabled={isRefreshing} // Disable button while refreshing
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              <span>{isRefreshing ? 'Refreshing...' : 'Refresh'}</span>
            </button>
            <button
              onClick={() => setShowMap(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              <Map className="w-4 h-4" />
              <span>Map</span>
            </button>
          </div>
        </div>

        {availableSensors.length === 0 ? ( // Check if there are no available sensors
          <div className="text-center py-12">
            <p className="text-xl text-gray-600">No sensors available</p>
            <p className="text-gray-500 mt-2">Contact administrator for sensor access</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {availableSensors.map((sensor) => ( // Map through available sensors to create SensorCard components
              <SensorCard
                key={sensor.equipmentId} // Unique key for each sensor card
                name={sensor.name} // Sensor name
                id={sensor.equipmentId} // Sensor ID
                apiId={sensor.apiId} // Sensor API ID
                onViewDetails={() => navigate(`/user/sensors/${sensor.equipmentId}`)} // Navigate to sensor details
              />
            ))}
          </div>
        )}
      </main>
      {showMap && (
        <MapPopup
          sensors={availableSensors}
          signalData={sensorSignalData}
          onClose={() => setShowMap(false)}
        />
      )}
    </div>
  );
};

export default SensorList; // Export the SensorList component for use in other parts of the application