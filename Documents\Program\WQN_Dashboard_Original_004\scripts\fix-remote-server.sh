#!/bin/bash

# Fix Remote Server - Application Files Not Found
# Run this script on the remote server (**************)

echo "🔧 Fixing Remote Server - Application Files Not Found"
echo "====================================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_status() {
    if [ "$2" = "OK" ]; then
        echo -e "${GREEN}✅ $1${NC}"
    elif [ "$2" = "WARN" ]; then
        echo -e "${YELLOW}⚠️  $1${NC}"
    elif [ "$2" = "ERROR" ]; then
        echo -e "${RED}❌ $1${NC}"
    else
        echo -e "${BLUE}ℹ️  $1${NC}"
    fi
}

# Step 1: Check current directory
echo "📁 Step 1: Checking current directory..."
if [ ! -f "package.json" ]; then
    print_status "Not in WQN Dashboard directory. Please cd to the correct directory." "ERROR"
    exit 1
fi
print_status "Found package.json - in correct directory" "OK"

# Step 2: Check Node.js and npm
echo ""
echo "🔧 Step 2: Checking Node.js and npm..."
if ! command -v node &> /dev/null; then
    print_status "Node.js not found. Please install Node.js 18+" "ERROR"
    exit 1
fi

NODE_VERSION=$(node --version)
print_status "Node.js version: $NODE_VERSION" "OK"

if ! command -v npm &> /dev/null; then
    print_status "npm not found. Please install npm" "ERROR"
    exit 1
fi
print_status "npm version: $(npm --version)" "OK"

# Step 3: Install dependencies
echo ""
echo "📦 Step 3: Installing dependencies..."
print_status "Running npm ci --production..." "INFO"
npm ci --production

if [ $? -eq 0 ]; then
    print_status "Dependencies installed successfully" "OK"
else
    print_status "Failed to install dependencies" "ERROR"
    exit 1
fi

# Step 4: Create production environment file
echo ""
echo "⚙️  Step 4: Setting up production environment..."
if [ ! -f ".env.production" ]; then
    print_status "Creating .env.production file..." "INFO"
    cat > .env.production << 'EOF'
# Production Environment Configuration
NODE_ENV=production
PORT=3001
HOST=0.0.0.0

# Database Configuration
DATABASE_URL=sqlite:./src/server/database/water_quality.sqlite

# JWT Configuration
JWT_SECRET=your-super-secure-64-character-jwt-secret-for-production-use-only-change-this
JWT_EXPIRES_IN=24h
JWT_ISSUER=wqn-dashboard
JWT_AUDIENCE=wqn-users

# CORS Configuration
CORS_ORIGIN=http://**************:3001,https://**************:3001,http://localhost:3001,https://localhost:3001

# Security
SESSION_SECRET=your-super-secure-64-character-session-secret-for-production-change-this
BCRYPT_SALT_ROUNDS=12

# External API Configuration (update with your actual values)
VITE_API_KEY=your_actual_api_key
VITE_API_SECRET=your_actual_api_secret
VITE_ACCOUNT=your_actual_account
VITE_TOKEN_URL=https://vip.boqucloud.com/api/token/get
VITE_MONITOR_OPEN_URL=https://vip.boqucloud.com/api/eg/monitor/open
VITE_SENSOR_DATA_URL=https://vip.boqucloud.com/api/eg/signal/value
EOF
    print_status ".env.production created" "OK"
else
    print_status ".env.production already exists" "OK"
fi

# Step 5: Build the application
echo ""
echo "🏗️  Step 5: Building the application..."
print_status "Running production build..." "INFO"

# Clean previous build
if [ -d "dist" ]; then
    rm -rf dist
    print_status "Cleaned previous build" "OK"
fi

# Run the build
npm run build

if [ $? -eq 0 ]; then
    print_status "Build completed successfully" "OK"
else
    print_status "Build failed. Trying alternative build command..." "WARN"
    npm run build:prod
    
    if [ $? -eq 0 ]; then
        print_status "Alternative build completed successfully" "OK"
    else
        print_status "Build failed. Please check the error messages above." "ERROR"
        exit 1
    fi
fi

# Step 6: Verify build output
echo ""
echo "🔍 Step 6: Verifying build output..."
if [ -d "dist" ]; then
    print_status "dist directory exists" "OK"
    
    if [ -f "dist/index.html" ]; then
        print_status "index.html found in dist directory" "OK"
    else
        print_status "index.html NOT found in dist directory" "ERROR"
        echo "Contents of dist directory:"
        ls -la dist/
        exit 1
    fi
    
    # Check for assets
    if [ -d "dist/assets" ]; then
        print_status "assets directory found" "OK"
        ASSET_COUNT=$(find dist/assets -type f | wc -l)
        print_status "Found $ASSET_COUNT asset files" "INFO"
    else
        print_status "assets directory not found" "WARN"
    fi
else
    print_status "dist directory does not exist" "ERROR"
    exit 1
fi

# Step 7: Initialize database
echo ""
echo "🗄️  Step 7: Initializing database..."
if [ ! -f "src/server/database/water_quality.sqlite" ]; then
    print_status "Database not found, running migration..." "INFO"
    npm run migrate
    
    if [ $? -eq 0 ]; then
        print_status "Database migration completed" "OK"
    else
        print_status "Database migration failed" "ERROR"
        exit 1
    fi
else
    print_status "Database already exists" "OK"
fi

# Reset admin password
print_status "Resetting admin password..." "INFO"
npm run reset-admin-password

# Step 8: Configure firewall (if needed)
echo ""
echo "🛡️  Step 8: Checking firewall configuration..."
if command -v ufw &> /dev/null; then
    if ufw status 2>/dev/null | grep -q "3001"; then
        print_status "Port 3001 already allowed in UFW" "OK"
    else
        print_status "Adding port 3001 to UFW firewall..." "INFO"
        sudo ufw allow 3001/tcp
        print_status "Port 3001 added to firewall" "OK"
    fi
elif command -v firewall-cmd &> /dev/null; then
    if firewall-cmd --list-ports 2>/dev/null | grep -q "3001"; then
        print_status "Port 3001 already allowed in firewalld" "OK"
    else
        print_status "Adding port 3001 to firewalld..." "INFO"
        sudo firewall-cmd --permanent --add-port=3001/tcp
        sudo firewall-cmd --reload
        print_status "Port 3001 added to firewall" "OK"
    fi
else
    print_status "No firewall management tool found" "INFO"
fi

# Step 9: Install and configure PM2
echo ""
echo "🔄 Step 9: Setting up process manager..."
if ! command -v pm2 &> /dev/null; then
    print_status "Installing PM2..." "INFO"
    npm install -g pm2
    print_status "PM2 installed" "OK"
else
    print_status "PM2 already installed" "OK"
fi

# Stop any existing processes
pm2 stop all 2>/dev/null || true
pm2 delete all 2>/dev/null || true

# Step 10: Start the application
echo ""
echo "🚀 Step 10: Starting the application..."
print_status "Starting WQN Dashboard with PM2..." "INFO"

# Start the application
pm2 start scripts/start-production.js --name wqn-dashboard

if [ $? -eq 0 ]; then
    print_status "Application started successfully" "OK"
    
    # Save PM2 configuration
    pm2 save
    
    # Setup PM2 to start on boot
    pm2 startup
    
    print_status "PM2 configuration saved" "OK"
else
    print_status "Failed to start application" "ERROR"
    exit 1
fi

# Step 11: Test the application
echo ""
echo "🧪 Step 11: Testing the application..."
sleep 5  # Wait for application to start

# Test health endpoint
if curl -s http://localhost:3001/health > /dev/null; then
    print_status "Health endpoint responding" "OK"
else
    print_status "Health endpoint not responding" "WARN"
fi

# Test main application
if curl -s http://localhost:3001/ | grep -q "Ecocoast Water Quality Monitor"; then
    print_status "Main application loading correctly" "OK"
else
    print_status "Main application may have issues" "WARN"
fi

# Final status
echo ""
echo "📊 Final Status:"
echo "================"
pm2 status

echo ""
echo "🎉 Setup Complete!"
echo "=================="
print_status "WQN Dashboard is now running on http://**************:3001" "OK"
print_status "Login with: admin / admin@123" "INFO"
print_status "Check logs with: pm2 logs wqn-dashboard" "INFO"
print_status "Restart with: pm2 restart wqn-dashboard" "INFO"

echo ""
echo "🔗 Access URLs:"
echo "• Main Application: http://**************:3001"
echo "• Health Check: http://**************:3001/health"
echo "• API: http://**************:3001/api"

echo ""
echo "✅ The 'Application files not found' error should now be resolved!"
