import React from 'react';
import { NavLink } from 'react-router-dom';
import {
  LayoutDashboard, // Icon for the dashboard
  Radio, // Icon for sensor management
  Users, // Icon for user management
  Shield, // Icon for access control
  Settings // Icon for settings
} from 'lucide-react';
import logoImage from '../../assets/images/logo.png';

// Array of navigation items, each containing an icon, label, and path
const navItems = [
  { icon: LayoutDashboard, label: 'Dashboard', path: '/admin' },
  { icon: Radio, label: 'Sensor Management', path: '/admin/sensors' },
  { icon: Users, label: 'User Management', path: '/admin/users' },
  { icon: Shield, label: 'Access Control', path: '/admin/access' },
  { icon: Settings, label: 'Settings', path: '/admin/settings' },
];

// Sidebar component that renders the navigation menu
function Sidebar() {
  return (
    <div className="w-64 bg-white border-r border-gray-200 h-screen">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <img src={logoImage} alt="Logo" className="w-512 h-8" />
          {/* Logo image for the sidebar */}
        </div>
      </div>
      <nav className="p-4 space-y-1">
        {/* Map through navItems to create NavLink for each item */}
        {navItems.map(({ icon: Icon, label, path }) => (
          <NavLink
            key={path} // Unique key for each NavLink
            to={path} // Path to navigate to
            end={path === '/admin'} // Mark as active if it's the admin path
            className={({ isActive }) =>
              `flex items-center space-x-3 px-4 py-2.5 rounded-lg transition-colors ${
                isActive
                  ? 'bg-blue-50 text-blue-600' // Active link styles
                  : 'text-gray-700 hover:bg-gray-50' // Inactive link styles
              }`
            }
          >
            <Icon className="w-5 h-5" /> {/* Render the icon */}
            <span>{label}</span> {/* Display the label */}
          </NavLink>
        ))}
      </nav>
    </div>
  );
}

export default Sidebar; // Export the Sidebar component for use in other parts of the application