import crypto from 'crypto';

/**
 * Security configuration for the application
 */

// JWT Configuration - using lazy evaluation to avoid early environment variable access
let _jwtConfig: any = null;

export const JWT_CONFIG = {
  get secret() {
    if (!_jwtConfig) {
      _jwtConfig = initializeJWTConfig();
    }
    return _jwtConfig.secret;
  },

  get expiresIn() {
    return process.env.JWT_EXPIRES_IN || '24h';
  },

  // JWT Algorithm
  algorithm: 'HS256' as const,

  // Issuer and audience for additional security
  get issuer() {
    return process.env.JWT_ISSUER || 'wqn-dashboard';
  },

  get audience() {
    return process.env.JWT_AUDIENCE || 'wqn-users';
  }
};

function initializeJWTConfig() {
  const secret = process.env.JWT_SECRET;

  if (!secret) {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('JWT_SECRET environment variable is required in production');
    }

    // Generate a secure random secret for development
    const generatedSecret = crypto.randomBytes(64).toString('hex');
    console.warn('⚠️  WARNING: Using generated JWT secret. Set JWT_SECRET environment variable for production.');
    console.warn(`Generated secret: ${generatedSecret}`);
    return { secret: generatedSecret };
  }

  // Validate secret strength
  if (secret.length < 32) {
    throw new Error('JWT_SECRET must be at least 32 characters long');
  }

  if (secret === 'your-secret-key' || secret === 'your-super-secret-jwt-key-change-this-in-production') {
    throw new Error('JWT_SECRET cannot use default values. Please set a secure secret.');
  }

  return { secret };
}

// Password Configuration
export const PASSWORD_CONFIG = {
  // bcrypt salt rounds
  saltRounds: parseInt(process.env.BCRYPT_ROUNDS || '12'),

  // Password requirements
  minLength: 8,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,

  // Password history (prevent reuse of last N passwords)
  historyCount: 5
};

// Session Configuration - using lazy evaluation
let _sessionConfig: any = null;

export const SESSION_CONFIG = {
  get secret() {
    if (!_sessionConfig) {
      _sessionConfig = initializeSessionConfig();
    }
    return _sessionConfig.secret;
  },

  maxAge: 24 * 60 * 60 * 1000, // 24 hours
  get secure() {
    return process.env.NODE_ENV === 'production';
  },
  httpOnly: true,
  sameSite: 'strict' as const
};

function initializeSessionConfig() {
  const secret = process.env.SESSION_SECRET;

  if (!secret) {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('SESSION_SECRET environment variable is required in production');
    }

    // Generate a secure random secret for development
    const generatedSecret = crypto.randomBytes(64).toString('hex');
    console.warn('⚠️  WARNING: Using generated session secret. Set SESSION_SECRET environment variable for production.');
    return { secret: generatedSecret };
  }

  if (secret.length < 32) {
    throw new Error('SESSION_SECRET must be at least 32 characters long');
  }

  return { secret };
}

// Rate Limiting Configuration
export const RATE_LIMIT_CONFIG = {
  // General API rate limiting
  general: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.',
    standardHeaders: true,
    legacyHeaders: false
  },

  // Strict rate limiting for authentication endpoints
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // limit each IP to 5 login attempts per windowMs
    message: 'Too many login attempts, please try again later.',
    skipSuccessfulRequests: true,
    skipFailedRequests: false
  },

  // Rate limiting for password change
  passwordChange: {
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 3, // limit each IP to 3 password changes per hour
    message: 'Too many password change attempts, please try again later.'
  }
};

// CORS Configuration - using lazy evaluation
let _corsConfig: any = null;

export const CORS_CONFIG = {
  get origin() {
    if (!_corsConfig) {
      _corsConfig = initializeCorsConfig();
    }
    return _corsConfig.origin;
  },

  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  exposedHeaders: ['X-Total-Count'],
  maxAge: 86400 // 24 hours
};

function initializeCorsConfig() {
  const origins = process.env.CORS_ORIGIN;
  if (origins) {
    const allowedOrigins = origins.split(',').map(origin => origin.trim());

    // Add dynamic CORS check for local network IPs
    return {
      origin: (origin, callback) => {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin) return callback(null, true);

        // Check if origin is in allowed list
        if (allowedOrigins.includes(origin)) {
          return callback(null, true);
        }

        // Allow local network IPs (192.168.x.x, 10.x.x.x, 172.16-31.x.x)
        const url = new URL(origin);
        const hostname = url.hostname;

        if (hostname === 'localhost' ||
            hostname === '127.0.0.1' ||
            hostname.startsWith('192.168.') ||
            hostname.startsWith('10.') ||
            (hostname.startsWith('172.') &&
             parseInt(hostname.split('.')[1]) >= 16 &&
             parseInt(hostname.split('.')[1]) <= 31)) {
          return callback(null, true);
        }

        // Reject other origins
        callback(new Error('Not allowed by CORS'));
      }
    };
  }

  // Default origins for development
  if (process.env.NODE_ENV === 'development') {
    return { origin: true }; // Allow all origins in development
  }

  // For production, allow local network access if CORS_ORIGIN not set
  console.warn('⚠️  WARNING: CORS_ORIGIN not set, allowing local network access');
  return { origin: true }; // Allow all origins (not recommended for public production)
}

// Get network IPs for CSP configuration
import { networkInterfaces } from 'os';

function getNetworkIPs() {
  const interfaces = networkInterfaces();
  const ips = [];

  for (const interfaceName in interfaces) {
    const interfaceList = interfaces[interfaceName];
    if (interfaceList) {
      for (const iface of interfaceList) {
        if (iface.family === 'IPv4' && !iface.internal) {
          ips.push(iface.address);
        }
      }
    }
  }

  return ips;
}

// Generate CSP sources for network IPs
function generateCSPSources() {
  const networkIPs = getNetworkIPs();
  const sources = ["'self'"];

  // Add localhost variants
  sources.push("http://localhost:3001", "https://localhost:3001");
  sources.push("http://127.0.0.1:3001", "https://127.0.0.1:3001");

  // Add network IPs
  for (const ip of networkIPs) {
    sources.push(`http://${ip}:3001`, `https://${ip}:3001`);
  }

  return sources;
}

// Security Headers Configuration
export const SECURITY_HEADERS_CONFIG = {
  contentSecurityPolicy: process.env.NODE_ENV === 'production' ? {
    directives: {
      defaultSrc: generateCSPSources(),
      styleSrc: [...generateCSPSources(), "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: [...generateCSPSources(), "https://fonts.gstatic.com"],
      imgSrc: [...generateCSPSources(), "data:", "https:"],
      scriptSrc: [...generateCSPSources(), "'unsafe-eval'"], // Allow eval for Chart.js
      connectSrc: [...generateCSPSources(), "https://vip.boqucloud.com"],
      objectSrc: ["'none'"],
      mediaSrc: generateCSPSources(),
      frameSrc: ["'none'"],
      // Explicitly do NOT include upgrade-insecure-requests for local development
      upgradeInsecureRequests: null
    }
  } : false, // Disable CSP in development

  hsts: process.env.NODE_ENV === 'production' && process.env.ENABLE_HTTPS === 'true' ? {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true
  } : false, // Disable HSTS for local development

  crossOriginEmbedderPolicy: false,
  crossOriginOpenerPolicy: false, // Disable for HTTP origins
  crossOriginResourcePolicy: false, // Disable for local network access
  originAgentCluster: false, // Disable to prevent agent cluster conflicts

  referrerPolicy: 'no-referrer-when-downgrade' // More permissive for local development
};

/**
 * Validate all security configurations on startup
 */
export function validateSecurityConfig(): void {
  console.log('🔒 Validating security configuration...');

  // Validate JWT configuration
  if (JWT_CONFIG.secret.length < 32) {
    throw new Error('JWT secret is too weak');
  }

  // Validate password configuration
  if (PASSWORD_CONFIG.saltRounds < 10) {
    throw new Error('bcrypt salt rounds too low (minimum 10)');
  }

  // Validate session configuration
  if (SESSION_CONFIG.secret.length < 32) {
    throw new Error('Session secret is too weak');
  }

  // Validate production-specific requirements
  if (process.env.NODE_ENV === 'production') {
    if (!process.env.JWT_SECRET) {
      throw new Error('JWT_SECRET must be set in production');
    }

    if (!process.env.SESSION_SECRET) {
      throw new Error('SESSION_SECRET must be set in production');
    }

    if (!process.env.CORS_ORIGIN) {
      console.warn('⚠️  WARNING: CORS_ORIGIN not set in production, using default same-origin policy');
    }

    if (!SESSION_CONFIG.secure) {
      console.warn('⚠️  WARNING: Session cookies are not secure in production');
    }
  }

  console.log('✅ Security configuration validated');
}
