#!/usr/bin/env node

/**
 * Network Connectivity Test Script
 * Tests server accessibility from different network interfaces
 */

import fetch from 'node-fetch';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

const COLORS = {
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  RESET: '\x1b[0m',
  BOLD: '\x1b[1m'
};

function log(message, color = COLORS.WHITE) {
  console.log(`${color}${message}${COLORS.RESET}`);
}

/**
 * Get local IP addresses
 */
async function getLocalIPs() {
  try {
    const { stdout } = await execAsync('ipconfig');
    const lines = stdout.split('\n');
    const ips = [];
    
    for (const line of lines) {
      if (line.includes('IPv4 Address')) {
        const match = line.match(/(\d+\.\d+\.\d+\.\d+)/);
        if (match && !match[1].startsWith('127.')) {
          ips.push(match[1]);
        }
      }
    }
    
    return ips;
  } catch (error) {
    log(`❌ Error getting IP addresses: ${error.message}`, COLORS.RED);
    return [];
  }
}

/**
 * Test server connectivity
 */
async function testConnection(url, description) {
  try {
    const response = await fetch(url, { 
      timeout: 5000,
      headers: {
        'User-Agent': 'WQN-Network-Test/1.0'
      }
    });
    
    if (response.ok) {
      log(`✅ ${description}: ${url} - Working`, COLORS.GREEN);
      return true;
    } else {
      log(`❌ ${description}: ${url} - HTTP ${response.status}`, COLORS.RED);
      return false;
    }
  } catch (error) {
    log(`❌ ${description}: ${url} - ${error.message}`, COLORS.RED);
    return false;
  }
}

/**
 * Check if port is listening
 */
async function checkPortListening(port) {
  try {
    const { stdout } = await execAsync(`netstat -an | findstr :${port}`);
    const isListening = stdout.includes('LISTENING');
    
    if (isListening) {
      log(`✅ Port ${port} is LISTENING`, COLORS.GREEN);
      
      // Check what it's bound to
      const lines = stdout.split('\n');
      for (const line of lines) {
        if (line.includes('LISTENING')) {
          const parts = line.trim().split(/\s+/);
          if (parts[1]) {
            const bindAddress = parts[1].split(':')[0];
            if (bindAddress === '0.0.0.0') {
              log(`✅ Server bound to all interfaces (0.0.0.0)`, COLORS.GREEN);
            } else if (bindAddress === '127.0.0.1') {
              log(`⚠️  Server bound to localhost only (127.0.0.1)`, COLORS.YELLOW);
            } else {
              log(`ℹ️  Server bound to: ${bindAddress}`, COLORS.BLUE);
            }
          }
        }
      }
    } else {
      log(`❌ Port ${port} is NOT listening`, COLORS.RED);
    }
    
    return isListening;
  } catch (error) {
    log(`❌ Error checking port ${port}: ${error.message}`, COLORS.RED);
    return false;
  }
}

/**
 * Test firewall connectivity
 */
async function testFirewall(port) {
  try {
    log(`🔥 Testing Windows Firewall for port ${port}...`, COLORS.CYAN);
    
    const { stdout } = await execAsync(`netsh advfirewall firewall show rule name=all | findstr ${port}`);
    
    if (stdout.trim()) {
      log(`ℹ️  Firewall rules found for port ${port}`, COLORS.BLUE);
      console.log(stdout);
    } else {
      log(`⚠️  No specific firewall rules found for port ${port}`, COLORS.YELLOW);
      log(`💡 Consider adding firewall rule: netsh advfirewall firewall add rule name="WQN Dashboard" dir=in action=allow protocol=TCP localport=${port}`, COLORS.CYAN);
    }
  } catch (error) {
    log(`⚠️  Could not check firewall rules: ${error.message}`, COLORS.YELLOW);
  }
}

/**
 * Main test function
 */
async function runNetworkTest() {
  const PORT = process.env.PORT || 3001;
  
  log(`${COLORS.BOLD}${COLORS.CYAN}🌐 WQN Dashboard Network Connectivity Test${COLORS.RESET}`);
  log(`${COLORS.BOLD}${COLORS.CYAN}===========================================${COLORS.RESET}\n`);
  
  // Step 1: Check if server is running
  log(`${COLORS.BOLD}📡 Step 1: Checking if server is running...${COLORS.RESET}`);
  const isListening = await checkPortListening(PORT);
  
  if (!isListening) {
    log(`\n❌ Server is not running on port ${PORT}`, COLORS.RED);
    log(`💡 Start the server first with: npm run dev or npm run start`, COLORS.CYAN);
    return;
  }
  
  // Step 2: Get local IP addresses
  log(`\n${COLORS.BOLD}🔍 Step 2: Getting local IP addresses...${COLORS.RESET}`);
  const localIPs = await getLocalIPs();
  log(`Found IP addresses: ${localIPs.join(', ')}`, COLORS.BLUE);
  
  // Step 3: Test localhost access
  log(`\n${COLORS.BOLD}🏠 Step 3: Testing localhost access...${COLORS.RESET}`);
  await testConnection(`http://127.0.0.1:${PORT}/health`, 'Localhost (127.0.0.1)');
  await testConnection(`http://localhost:${PORT}/health`, 'Localhost (hostname)');
  
  // Step 4: Test network access
  log(`\n${COLORS.BOLD}🌐 Step 4: Testing network access...${COLORS.RESET}`);
  for (const ip of localIPs) {
    await testConnection(`http://${ip}:${PORT}/health`, `Network IP (${ip})`);
  }
  
  // Step 5: Test API endpoints
  log(`\n${COLORS.BOLD}🔌 Step 5: Testing API endpoints...${COLORS.RESET}`);
  await testConnection(`http://127.0.0.1:${PORT}/api`, 'API Endpoint (localhost)');
  
  for (const ip of localIPs) {
    await testConnection(`http://${ip}:${PORT}/api`, `API Endpoint (${ip})`);
  }
  
  // Step 6: Check firewall
  log(`\n${COLORS.BOLD}🔥 Step 6: Checking firewall settings...${COLORS.RESET}`);
  await testFirewall(PORT);
  
  // Step 7: Recommendations
  log(`\n${COLORS.BOLD}💡 Recommendations:${COLORS.RESET}`);
  log(`1. Ensure HOST=0.0.0.0 in your .env file`, COLORS.CYAN);
  log(`2. Add firewall rule if needed: netsh advfirewall firewall add rule name="WQN Dashboard" dir=in action=allow protocol=TCP localport=${PORT}`, COLORS.CYAN);
  log(`3. Test from another device: http://[YOUR_IP]:${PORT}`, COLORS.CYAN);
  log(`4. Check router/network settings if still not accessible`, COLORS.CYAN);
}

// Run the test
runNetworkTest().catch(error => {
  log(`❌ Test failed: ${error.message}`, COLORS.RED);
  process.exit(1);
});
