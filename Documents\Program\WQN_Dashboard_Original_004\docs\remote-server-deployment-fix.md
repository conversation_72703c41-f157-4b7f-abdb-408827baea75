# 🌐 Remote Server Deployment Fix Guide

## 🎯 **Scenario: Server Hosted on **************:3001**

If your WQN Dashboard is deployed on a remote server at `**************:3001`, here's the comprehensive fix for the 500 login error.

## 🔍 **Remote Server Issues & Fixes**

### **Issue 1: Server Configuration**

#### **Fix 1.1: Environment Variables**
The remote server needs proper environment configuration:

```bash
# SSH into the remote server
ssh user@**************

# Navigate to application directory
cd /path/to/wqn-dashboard

# Create/update .env.production
nano .env.production
```

**Required .env.production content:**
```bash
# Database Configuration
DATABASE_URL=sqlite:./src/server/database/water_quality.sqlite
# Or for PostgreSQL:
# DATABASE_URL=postgresql://username:password@localhost:5432/wqn_dashboard

# JWT Configuration
JWT_SECRET=your-super-secure-64-character-jwt-secret-for-production-use-only
JWT_EXPIRES_IN=24h
JWT_ISSUER=wqn-dashboard
JWT_AUDIENCE=wqn-users

# Server Configuration
NODE_ENV=production
PORT=3001
HOST=0.0.0.0

# CORS Configuration - CRITICAL for remote access
CORS_ORIGIN=http://**************:3001,https://**************:3001,http://localhost:3001,https://localhost:3001

# Security
SESSION_SECRET=your-super-secure-64-character-session-secret-for-production
BCRYPT_SALT_ROUNDS=12

# External API Configuration
VITE_API_KEY=your_actual_api_key
VITE_API_SECRET=your_actual_api_secret
VITE_ACCOUNT=your_actual_account
VITE_TOKEN_URL=https://vip.boqucloud.com/api/token/get
VITE_MONITOR_OPEN_URL=https://vip.boqucloud.com/api/eg/monitor/open
VITE_SENSOR_DATA_URL=https://vip.boqucloud.com/api/eg/signal/value
```

#### **Fix 1.2: Generate Secure Secrets**
```bash
# Generate JWT secret
node -e "console.log('JWT_SECRET=' + require('crypto').randomBytes(64).toString('hex'))"

# Generate session secret
node -e "console.log('SESSION_SECRET=' + require('crypto').randomBytes(64).toString('hex'))"
```

### **Issue 2: Database Issues**

#### **Fix 2.1: Database Initialization**
```bash
# Check if database exists
ls -la src/server/database/

# If database doesn't exist, initialize it
npm run migrate

# Reset admin password
npm run reset-admin-password
```

#### **Fix 2.2: Database Permissions**
```bash
# Ensure proper permissions
chmod 644 src/server/database/water_quality.sqlite
chown www-data:www-data src/server/database/water_quality.sqlite
```

### **Issue 3: Server Process Management**

#### **Fix 3.1: Use Process Manager (PM2)**
```bash
# Install PM2 globally
npm install -g pm2

# Create PM2 ecosystem file
nano ecosystem.config.js
```

**ecosystem.config.js:**
```javascript
module.exports = {
  apps: [{
    name: 'wqn-dashboard',
    script: 'scripts/start-production.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3001,
      HOST: '0.0.0.0'
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
```

#### **Fix 3.2: Start with PM2**
```bash
# Stop any existing processes
pm2 stop all
pm2 delete all

# Start the application
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 to start on boot
pm2 startup
```

### **Issue 4: Firewall Configuration**

#### **Fix 4.1: Ubuntu/Debian Firewall**
```bash
# Check firewall status
sudo ufw status

# Allow port 3001
sudo ufw allow 3001/tcp

# Allow SSH (important!)
sudo ufw allow ssh

# Enable firewall
sudo ufw enable
```

#### **Fix 4.2: CentOS/RHEL Firewall**
```bash
# Check firewall status
sudo firewall-cmd --state

# Allow port 3001
sudo firewall-cmd --permanent --add-port=3001/tcp
sudo firewall-cmd --reload

# Verify
sudo firewall-cmd --list-ports
```

### **Issue 5: Reverse Proxy Setup (Recommended)**

#### **Fix 5.1: Nginx Configuration**
```bash
# Install Nginx
sudo apt update
sudo apt install nginx

# Create Nginx configuration
sudo nano /etc/nginx/sites-available/wqn-dashboard
```

**Nginx configuration:**
```nginx
server {
    listen 80;
    server_name **************;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    # Proxy to Node.js application
    location / {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # API specific configuration
    location /api/ {
        proxy_pass http://127.0.0.1:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS headers
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization";
    }
}
```

#### **Fix 5.2: Enable Nginx**
```bash
# Enable the site
sudo ln -s /etc/nginx/sites-available/wqn-dashboard /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

### **Issue 6: SSL/HTTPS Setup (Recommended)**

#### **Fix 6.1: Let's Encrypt SSL**
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d **************

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🧪 **Deployment Script**

Create an automated deployment script:

```bash
# Create deploy.sh
nano deploy.sh
```

**deploy.sh:**
```bash
#!/bin/bash

echo "🚀 Deploying WQN Dashboard to Production..."

# Update code
git pull origin main

# Install dependencies
npm ci --production

# Build application
npm run build

# Run database migrations
npm run migrate

# Restart application
pm2 restart wqn-dashboard

# Check status
pm2 status

echo "✅ Deployment complete!"
echo "🌐 Application available at: http://**************"
```

```bash
# Make executable
chmod +x deploy.sh
```

## 🔧 **Troubleshooting Remote Server**

### **Check Server Logs**
```bash
# PM2 logs
pm2 logs wqn-dashboard

# System logs
sudo journalctl -u nginx -f
sudo tail -f /var/log/nginx/error.log

# Application logs
tail -f logs/combined.log
```

### **Test Database Connection**
```bash
# Run diagnostics on remote server
npm run diagnose-login

# Test database directly
sqlite3 src/server/database/water_quality.sqlite ".tables"
```

### **Test API Endpoints**
```bash
# Test health endpoint
curl http://localhost:3001/health

# Test login endpoint
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"identifier":"admin","password":"admin@123"}'
```

## 📋 **Quick Deployment Checklist**

- [ ] ✅ Environment variables configured
- [ ] ✅ Database initialized with users
- [ ] ✅ PM2 process manager setup
- [ ] ✅ Firewall configured (port 3001 or 80/443)
- [ ] ✅ Nginx reverse proxy (optional but recommended)
- [ ] ✅ SSL certificate (for HTTPS)
- [ ] ✅ Application built and deployed
- [ ] ✅ Admin user password reset
- [ ] ✅ Logs monitoring setup

## 🌐 **Access URLs After Fix**

- **HTTP**: `http://**************` (via Nginx)
- **HTTPS**: `https://**************` (with SSL)
- **Direct**: `http://**************:3001` (if no reverse proxy)

## 🚨 **Security Considerations**

1. **Change default passwords** immediately
2. **Use strong JWT secrets** (64+ characters)
3. **Enable HTTPS** with valid SSL certificates
4. **Configure firewall** to only allow necessary ports
5. **Regular security updates** for OS and dependencies
6. **Monitor logs** for suspicious activity
7. **Backup database** regularly

---

**With these fixes, your remote server at `**************:3001` should handle login requests without 500 errors!**
